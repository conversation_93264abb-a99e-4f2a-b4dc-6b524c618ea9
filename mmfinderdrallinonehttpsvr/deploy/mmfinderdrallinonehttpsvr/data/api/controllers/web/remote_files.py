import logging
import urllib.parse

import httpx
from flask_restful import marshal_with, reqparse

import services
from controllers.common import helpers
from controllers.web.wraps import WebApiResource
from core.file import helpers as file_helpers
from core.helper import ssrf_proxy
from fields.file_fields import file_fields_with_signed_url, remote_file_info_fields
from services.file_service import FileService

# from wx.ext_wx_img import AesUtil

from .error import FileTooLargeError, UnsupportedFileTypeError

# 更改dns解析
# from urllib3.util import connection

# host_table = {
#     "mmbizcos-1307796349.cos.ap-shanghai.myqcloud.com": "***************:80",
# }
# # 保存原始的DNS解析函数
# _orig_create_connection = connection.create_connection


# def _dns_resolver(host):
#     if host in host_table:
#         logging.error("[liweizheng] 自定义DNS解析被调用")
#         return host_table[host]
#     else:
#         return host


# def patched_create_connection(address, *args, **kwargs):
#     host, port = address
#     hostname = _dns_resolver(host)
#     return _orig_create_connection((hostname, port), *args, **kwargs)
#     # 替换DNS解析函数


# connection.create_connection = patched_create_connection

# 发送请求
# import requests

# response = requests.get("https://example.com")
# # 恢复原始DNS解析函数
# connection.create_connection = _orig_create_connection


class RemoteFileInfoApi(WebApiResource):
    @marshal_with(remote_file_info_fields)
    def get(self, app_model, end_user, url):
        decoded_url = urllib.parse.unquote(url)
        resp = ssrf_proxy.head(decoded_url)
        if resp.status_code != httpx.codes.OK:
            # failed back to get method
            resp = ssrf_proxy.get(decoded_url, timeout=3)
        resp.raise_for_status()
        return {
            "file_type": resp.headers.get("Content-Type", "application/octet-stream"),
            "file_length": int(resp.headers.get("Content-Length", -1)),
        }


class RemoteFileUploadApi(WebApiResource):
    @marshal_with(file_fields_with_signed_url)
    def post(self, app_model, end_user):  # Add app_model and end_user parameters
        parser = reqparse.RequestParser()
        parser.add_argument("url", type=str, required=True, help="URL is required")
        # parser.add_argument("aes_key", type=str, required=False, help="wx cdn aes_key")
        args = parser.parse_args()

        url = args["url"]
        # aes_key=args.get('aes_key',None)
        timeout = 30
        # resp = ssrf_proxy.head(url=url)
        resp = ssrf_proxy.get(url=url, timeout=timeout, follow_redirects=True)
        resp.raise_for_status()

        file_info = helpers.guess_file_info_from_response(resp)

        if not FileService.is_file_size_within_limit(
            extension=file_info.extension, file_size=file_info.size
        ):
            raise FileTooLargeError

        content = resp.content

        try:
            upload_file = FileService.upload_file(
                filename=file_info.filename,
                content=content,
                mimetype=file_info.mimetype,
                user=end_user,
                source_url=url,
            )
        except services.errors.file.FileTooLargeError as file_too_large_error:
            raise FileTooLargeError(file_too_large_error.description)
        except services.errors.file.UnsupportedFileTypeError:
            raise UnsupportedFileTypeError

        return {
            "id": upload_file.id,
            "name": upload_file.name,
            "size": upload_file.size,
            "extension": upload_file.extension,
            "url": file_helpers.get_signed_file_url(upload_file_id=upload_file.id),
            "mime_type": upload_file.mime_type,
            "created_by": upload_file.created_by,
            "created_at": upload_file.created_at,
        }, 201
