from flask import request
from flask_restful import Resource, reqparse
from flask_restful.inputs import int_range
from flask_login import current_user
from core.app.app_config.easy_ui_based_app.agent.manager import AgentConfigManager
from core.tools.tool_manager import ToolManager
from sqlalchemy import or_
import logging
import pytz
from core.app.entities.app_invoke_entities import InvokeFrom
from controllers.service_api import api
from extensions.ext_database import db
from libs.helper import DatetimeString
from models.model import App, Conversation, EndUser, Message, MessageAgentThought, MessageAnnotation
from datetime import datetime
import wx.dao.app

def get_conversation_info(args, app_id):
    subquery = (
        db.session.query(
            Conversation.id.label("conversation_id"),
            EndUser.session_id.label("from_end_user_session_id")
        )
        .outerjoin(EndUser, Conversation.from_end_user_id == EndUser.id)
        .subquery()
    )
    dict = {
        "keyword": args.get("keyword"),
        "page": args.get("page"),
        "limit": args.get("limit"),
        "mode": args.get("mode"),
        "start": args.get("start"),
        "end": args.get("end"),
    }
    query = db.select(Conversation).where(Conversation.app_id == app_id)
    logging.info(f"AgentMessageDetailApi Conversation query {app_id}, query {dict}")

    if args["keyword"] is None:
        return {
            "success": False,
            "data": None
        }
    keyword = args["keyword"]
    keyword_filter = "%{}%".format(keyword)
    query = (
        query.join(
            Message,
            Message.conversation_id == Conversation.id,
        )
        .join(subquery, subquery.c.conversation_id == Conversation.id)
        .filter(
            or_(
                Message.query.ilike(keyword_filter),
                Message.answer.ilike(keyword_filter),
                Conversation.name.ilike(keyword_filter),
                Conversation.introduction.ilike(keyword_filter),
                subquery.c.from_end_user_session_id.ilike(keyword_filter),
            ),
        )
        .group_by(Conversation.id)
    )
    

    timezone = pytz.timezone('Asia/Shanghai')
    utc_timezone = pytz.utc
    if args["start"]:
            start_datetime = datetime.strptime(args["start"], "%Y-%m-%d %H:%M")
            start_datetime = start_datetime.replace(second=0)

            start_datetime_timezone = timezone.localize(start_datetime)
            start_datetime_utc = start_datetime_timezone.astimezone(utc_timezone)
            query = query.where(Conversation.created_at >= start_datetime_utc)
    
    if args["end"]:
            end_datetime = datetime.strptime(args["end"], "%Y-%m-%d %H:%M")
            end_datetime = end_datetime.replace(second=59)

            end_datetime_timezone = timezone.localize(end_datetime)
            end_datetime_utc = end_datetime_timezone.astimezone(utc_timezone)
            query = query.where(Conversation.created_at <= end_datetime_utc)
    
    if args.get("mode") == "advanced-chat":
        query = query.where(Conversation.invoke_from != InvokeFrom.DEBUGGER.value)
    query = query.order_by(Conversation.created_at.asc())
    conversations = db.paginate(query, page=args.get("page"), per_page=args.get("limit"), error_out=False)
    data = {}
    item = conversations.items[0] if conversations.total >= 1 else None
    if item:
        data = {
            "id": item.id,
            "name": item.name,
            "from_end_user_session_id": item.from_end_user_session_id,
            "from_end_user_id": item.from_end_user_id,
            "from_account_id": item.from_account_id
        }

    result = {
        "success": True,
        "data": data
    }
    return result


def get_message_info(conversation_id, app_id):
    logging.info(f"message conversation_id {conversation_id}, app_id {app_id}")
    message_list = db.session.query(Message).filter(
        Message.conversation_id == conversation_id,
        Message.app_id == app_id
    ).order_by(Message.created_at.desc()).all()
    if len(message_list) == 0:
        return {
            "result": False,
            "data": None,
        }
    message = message_list[0]
    length = len(message_list)
    message_obj = {
        "id": message.id,
        "query": message.query,
        "answer": message.answer,
        "conversation_id": message.conversation_id,
        "app_id": message.app_id,
        "agent_thoughts": message.agent_thoughts,
        "created_at": message.created_at,
        "provider_response_latency": message.provider_response_latency,
        "answer_tokens": message.answer_tokens,
        "message_tokens": message.message_tokens,
        "message_files": message.message_files,
    }
    return {
        "result": True,
        "data": {
            "message": message_obj,
            "length": length
        }
    }


def get_agent_log(app_id, conversation, message):
    logging.info(
        f"AgentMessageDetailApi get_agent_log app_id {app_id}, conversation {conversation.get("id")}, message {message.get("id")}")
    agent_thoughts: list[MessageAgentThought] = message.get("agent_thoughts")

    app = wx.dao.app.GetAppById(app_id)
    if app:
        app_model_config = app.app_model_config
        logging.info(f"app_model_config {app_model_config}, message {message}")
        result = {
            "meta": {
                "status": "success",
                "executor": None,
                "elapsed_time": message.get("provider_response_latency"),
                "total_tokens": message.get("answer_tokens") + message.get("message_tokens"),
                "agent_mode": app_model_config.agent_mode_dict.get("strategy", "react"),
                "iterations": len(agent_thoughts),
            },
            "iterations": [],
            "files": message.get("message_files"),
        }
        agent_config = AgentConfigManager.convert(app_model_config.to_dict())
        agent_tools = agent_config.tools
        logging.info(f"agent_config {agent_config}, agent_tools {agent_tools}")

        def find_agent_tool(tool_name: str):
            for agent_tool in agent_tools:
                if agent_tool.tool_name == tool_name:
                    return agent_tool

        for agent_thought in agent_thoughts:
            tools = agent_thought.tools
            tool_labels = agent_thought.tool_labels
            tool_meta = agent_thought.tool_meta
            tool_inputs = agent_thought.tool_inputs_dict
            tool_outputs = agent_thought.tool_outputs_dict
            tool_calls = []
            for tool in tools:
                tool_name = tool
                tool_label = tool_labels.get(tool_name, tool_name)
                tool_input = tool_inputs.get(tool_name, {})
                tool_output = tool_outputs.get(tool_name, {})
                tool_meta_data = tool_meta.get(tool_name, {})
                tool_config = tool_meta_data.get("tool_config", {})
                if tool_config.get("tool_provider_type", "") != "dataset-retrieval":
                    tool_icon = ToolManager.get_tool_icon(
                        tenant_id="cbe9cf47-e21a-4d09-b57c-253ac186c90c",
                        provider_type=tool_config.get("tool_provider_type", ""),
                        provider_id=tool_config.get("tool_provider", ""),
                    )
                    if not tool_icon:
                        tool_entity = find_agent_tool(tool_name)
                        if tool_entity:
                            tool_icon = ToolManager.get_tool_icon(
                                tenant_id="cbe9cf47-e21a-4d09-b57c-253ac186c90c",
                                provider_type=tool_entity.provider_type,
                                provider_id=tool_entity.provider_id,
                            )
                else:
                    tool_icon = ""

                tool_calls.append(
                    {
                        "status": "success" if not tool_meta_data.get("error") else "error",
                        "error": tool_meta_data.get("error"),
                        "time_cost": tool_meta_data.get("time_cost", 0),
                        "tool_name": tool_name,
                        "tool_label": tool_label,
                        "tool_input": tool_input,
                        "tool_output": tool_output,
                        "tool_parameters": tool_meta_data.get("tool_parameters", {}),
                        "tool_icon": tool_icon,
                    }
                )

            result["iterations"].append(
                {
                    "tokens": agent_thought.tokens,
                    "tool_calls": tool_calls,
                    "tool_raw": {
                        "inputs": agent_thought.tool_input,
                        "outputs": agent_thought.observation,
                    },
                    "thought": agent_thought.thought,
                    "created_at": agent_thought.created_at.isoformat(),
                    "files": agent_thought.files,
                }
            )

        return result


class AgentMessageDetailApi(Resource):
    def get(self, app_id):
        parser = reqparse.RequestParser()
        parser.add_argument("keyword", type=str, location="args")
        parser.add_argument("page", type=int_range(1, 99999), required=False, default=1, location="args")
        parser.add_argument("limit", type=int_range(1, 100), required=False, default=20, location="args")
        parser.add_argument("mode", type=str, location="args")
        parser.add_argument("start", type=DatetimeString("%Y-%m-%d %H:%M"), location="args")
        parser.add_argument("end", type=DatetimeString("%Y-%m-%d %H:%M"), location="args")
        args = parser.parse_args()
        conversation_info = get_conversation_info(args, app_id)
        if not conversation_info.get("success"):
            return {
                "success": False,
                "conversation": None,
                "message": "No conversation found"
            }
        # 拿到conversation
        conversation = conversation_info.get("data")
        message_info = get_message_info(conversation_id=conversation.get("id"), app_id=app_id)
        if message_info.get("result") == False:
            return {
                "success": False,
                "conversation": conversation.get("id"),
                "message": "No message found"
            }
        # 拿到Message信息
        message_data = message_info.get("data")
        message = message_data.get("message")
        agent_log = get_agent_log(app_id, conversation, message)
        return {
            "success": True,
            "conversation": conversation.get("id"),
            "message": {
                "id": message.get("id"),
                "query": message.get("query"),
                "answer": message.get("answer"),
                "length": message_data.get("length")
            },
            "agent_log": agent_log
        }


api.add_resource(AgentMessageDetailApi, "/agent/<uuid:app_id>/chat-conversation")
