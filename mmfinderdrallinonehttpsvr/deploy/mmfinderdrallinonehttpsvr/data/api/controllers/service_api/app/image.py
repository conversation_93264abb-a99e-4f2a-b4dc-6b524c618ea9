from flask import request, Response
from flask_restful import Resource, reqparse
import logging

import base64
from controllers.service_api import api
from fields.file_fields import file_fields
from wx.img_util import download_wx_cdn_resource_and_decrypt
import time
from core.helper import ssrf_proxy


def get_download_file_res(url, aes_key):
    logging.info(
        f"AesKeyImageFileDownloadApi download_file called url {url} aes_key {aes_key}"
    )
    before_req_time = int(time.time() * 1000)
    res = download_wx_cdn_resource_and_decrypt(url, aes_key)
    after_req_time = int(time.time() * 1000)
    logging.info(
        f"AesKeyImageFileDownloadApi image download download_file time cost {after_req_time - before_req_time}"
    )
    return res


class AesKeyImageFileDownloadApi(Resource):
    def post(self):
        # 参数解析
        parser = reqparse.RequestParser()
        parser.add_argument("url", type=str, required=True, location="json")
        parser.add_argument("aes_key", type=str, required=True, location="json")
        args = parser.parse_args()
        url = args.get("url")
        aes_key = args.get("aes_key")
        req_dict = {"url": url, "aes_key": aes_key}
        try:
            res = get_download_file_res(url=url, aes_key=aes_key)
            png_base64 = base64.b64encode(res).decode("utf-8")
            logging.info(f"AesKeyImageFileDownloadApi called {req_dict}")
            data_url = f"data:image/png;base64,{png_base64}"
            return {"status": "success", "data_url": data_url}
        except Exception as e:
            logging.error(f"AesKeyImageFileDownloadApi called {req_dict} error {e}")
            return {"status": "error", "error": str(e)}


class ImageFileDownloadApi(Resource):
    def post(self):
        # 参数解析
        parser = reqparse.RequestParser()
        parser.add_argument(
            "url", type=str, required=True, help="Image URL is required"
        )
        args = parser.parse_args()
        image_url = args.get("url")
        logging.error(f"[liweizheng] ImageFileDownloadApi called url {image_url}")

        if not image_url:
            return {"error": "Missing url parameter"}, 400

        try:
            resp = ssrf_proxy.get(url=image_url, timeout=5, follow_redirects=True)
            resp.raise_for_status()

            content_type = resp.headers.get("Content-Type", "application/octet-stream")
            # 返回图片字节流
            return Response(resp.content, content_type=content_type)

        except Exception as e:
            return {"error": f"Failed to download image: {str(e)}"}, 500


api.add_resource(AesKeyImageFileDownloadApi, "/image/download/aes_key")
# api.add_resource(ImageFileDownloadApi, "/image/download")
