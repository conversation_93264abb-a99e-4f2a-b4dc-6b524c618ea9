import json
import logging

from flask_restful import Resource, fields, marshal_with, reqparse
from flask_restful.inputs import int_range
from core.tools.entities.tool_entities import ToolInvokeMessage, ToolParameter
from services.workflow_run_service import WorkflowRunService
from werkzeug.exceptions import InternalServerError
from flask import g
from wx.rainbow_conf import hit_rate_limit
from core.tools.tool_manager import ToolManager
from core.tools.tool_engine import ToolEngine
from core.tools.tool.tool import Tool

from controllers.service_api import api
from controllers.service_api.app.error import (
    AppRateLimitError,
    CompletionRequestError,
    NotWorkflowAppError,
    ProviderModelCurrentlyNotSupportError,
    ProviderNotInitializeError,
    ProviderQuotaExceededError,
)
from controllers.service_api.wraps import (
    FetchUserArg,
    WhereisUserArg,
    validate_app_token,
)
from core.app.apps.base_app_queue_manager import AppQueueManager
from core.app.entities.app_invoke_entities import InvokeFrom
from core.errors.error import (
    AppInvokeQuotaExceededError,
    ModelCurrentlyNotSupportError,
    ProviderTokenNotInitError,
    QuotaExceededError,
)
import pulsar
from core.model_runtime.errors.invoke import InvokeError
from extensions.ext_database import db
from fields.workflow_app_log_fields import workflow_app_log_pagination_fields
from libs import helper
from models.model import App, AppMode, EndUser
from models.workflow import WorkflowRun
from services.app_generate_service import AppGenerateService
from services.workflow_app_service import WorkflowAppService
from fields.workflow_run_fields import workflow_run_node_execution_list_fields

logger = logging.getLogger(__name__)

workflow_run_fields = {
    "id": fields.String,
    "workflow_id": fields.String,
    "status": fields.String,
    "inputs": fields.Raw,
    "outputs": fields.Raw,
    "error": fields.String,
    "total_steps": fields.Integer,
    "total_tokens": fields.Integer,
    "created_at": fields.DateTime,
    "finished_at": fields.DateTime,
    "elapsed_time": fields.Float,
}

from openai import OpenAI


def get_adam_sys(system, prompt):

    client = OpenAI(
        api_key="EMPTY",
        base_url="http://drhttpsvr.polaris:8000/v1/llm_luban-DeepSeek-R1-671B-train/",
    )

    chat_response = client.chat.completions.create(
        model="llm_luban-DeepSeek-R1-671B-train",  # 此处以 deepseek-v3 为例，可按需更换模型名称
        messages=[
            {"role": "system", "content": system},
            {"role": "user", "content": prompt},
        ],
        stream=False,
    )

    return chat_response.choices[0].message.content


def extract_tool_description(tool: Tool) -> dict:
    result = {
        "name": tool.identity.name,
        "label": tool.identity.label.en_US,
        "description": tool.description.human.model_dump(),
        "parameters": [],
        "tool_call_input_template": {"tool_name": tool.identity.name, "tool_input": {}},
    }
    for parameter in tool.parameters:
        result["parameters"].append(
            {
                "name": parameter.name,
                "description": parameter.human_description.en_US,
                "type": parameter.type.as_normal_type(),  # to be convert to string
            }
        )
        result["tool_call_input_template"]["tool_input"][parameter.name] = (
            "$" + parameter.name
        )
    return result


def parse_tool_resp(response_list: list[ToolInvokeMessage]) -> dict:
    result = ""
    for response in response_list:
        if response.type == ToolInvokeMessage.MessageType.TEXT:
            # result += response.message
            pass
        elif response.type == ToolInvokeMessage.MessageType.LINK:
            # result += f"result link: {response.message}. please tell user to check it."
            pass
        elif response.type in {
            ToolInvokeMessage.MessageType.IMAGE_LINK,
            ToolInvokeMessage.MessageType.IMAGE,
        }:
            # result += (
            #     "image has been created and sent to user already, you do not need to create it,"
            #     " just tell the user to check it now."
            # )
            pass
        elif response.type == ToolInvokeMessage.MessageType.JSON:
            result = response.message
        else:
            # result += f"tool response: {response.message}."
            pass
    return result


class ToolRunApi(Resource):
    @validate_app_token(
        fetch_user_arg=FetchUserArg(fetch_from=WhereisUserArg.JSON, required=False)
    )
    def post(self, app_model: App, end_user: EndUser):
        """
        Run tool
        """
        logging.error(f"[liweizheng] enter toolrunapi")
        app_mode = AppMode.value_of(app_model.mode)

        parser = reqparse.RequestParser()
        parser.add_argument(
            "inputs", type=dict, required=True, nullable=False, location="json"
        )
        parser.add_argument(
            "name", type=str, required=True, nullable=False, location="json"
        )
        parser.add_argument("files", type=list, required=False, location="json")
        parser.add_argument(
            "response_mode",
            type=str,
            choices=["blocking", "streaming"],
            location="json",
        )
        args = parser.parse_args()

        streaming = args.get("response_mode") == "streaming"

        if hit_rate_limit(app_model.id):
            raise AppRateLimitError()

        try:
            tool_instances = ToolManager.get_tools(True)
            logging.error(f"[liweizheng] tool_instances: {repr(tool_instances)}")
            tool_instance = tool_instances[args["name"]]
            logging.error(f"[liweizheng] tool_instance detail: {repr(tool_instance)}")
            tool_instance.parameters

            tool_desc = json.dumps(extract_tool_description(tool_instance))
            logging.error(f"[liweizheng] tool_instance desc: {repr(tool_desc)}")

            # runtime = {
            #     "tenant_id": tenant_id,
            #     "credentials": {},
            #     "invoke_from": invoke_from,
            #     "tool_invoke_from": tool_invoke_from,
            #     "app_id": app_id,
            # }
            tool_instance.runtime = Tool.Runtime(**{"invoke_from": InvokeFrom.DEBUGGER})
            meta, response_list = ToolEngine._invoke(
                tool_instance, args["inputs"], end_user.id
            )
            response_list = tool_instance.invoke(end_user.id, args["inputs"])
            logging.error(f"[liweizheng] response: {repr(response_list)}")
            # plain_text = ToolEngine._convert_tool_response_to_str(response_list)

            # logging.error(f"[liweizheng] plain_text of response: {plain_text}")
            return {"result": parse_tool_resp(response_list), "meta": meta.to_dict()}

        except ProviderTokenNotInitError as ex:
            raise ProviderNotInitializeError(ex.description)
        except QuotaExceededError:
            raise ProviderQuotaExceededError()
        except ModelCurrentlyNotSupportError:
            raise ProviderModelCurrentlyNotSupportError()
        except InvokeError as e:
            raise CompletionRequestError(e.description)
        except (ValueError, AppInvokeQuotaExceededError) as e:
            raise e
        except Exception as e:
            logging.exception(f"internal server error. e: {repr(e)}")
            raise InternalServerError()


class AgentRunApi(Resource):
    @validate_app_token(
        fetch_user_arg=FetchUserArg(fetch_from=WhereisUserArg.JSON, required=False)
    )
    def post(self, app_model: App, end_user: EndUser):
        """
        Run tool
        """
        logging.error(f"[liweizheng] enter agentrunapi")
        app_mode = AppMode.value_of(app_model.mode)

        parser = reqparse.RequestParser()
        parser.add_argument(
            "inputs", type=dict, required=True, nullable=False, location="json"
        )
        parser.add_argument(
            "name", type=str, required=True, nullable=False, location="json"
        )
        parser.add_argument("files", type=list, required=False, location="json")
        parser.add_argument(
            "response_mode",
            type=str,
            choices=["blocking", "streaming"],
            location="json",
        )
        args = parser.parse_args()

        streaming = args.get("response_mode") == "streaming"

        if hit_rate_limit(app_model.id):
            raise AppRateLimitError()

        try:
            inputs = args["inputs"]
            tools = inputs["tools"]
            query = inputs["query"]
            system = "你是一个聪明的Tool Agent"
            tools_introduce = "你可以根据问题选择工具，输出需要使用的工具及其参数，可用的工具描述如下：\n"

            tool_instances = ToolManager.get_tools(True)
            logging.error(f"[liweizheng] tool_instances: {repr(tool_instances)}")
            for tool in tools:
                tool_instance = tool_instances[tool]
                # logging.error(
                #     f"[liweizheng] tool_instance detail: {repr(tool_instance)}"
                # )
                tool_desc = json.dumps(extract_tool_description(tool_instance))
                tools_introduce += tool_desc + "\n\n"
                logging.error(f"[liweizheng] tool_instance desc: {tool_desc}")

            tools_introduce += """上面的每个json结构体都代表一个工具的介绍，其中
            name是工具的名称，label是工具的标签，description是工具的描述，parameters是工具的输入参数介绍
            parameters中的每个json结构体代表一个参数的介绍，其中name是参数的名称，description是参数的描述，type是参数的类型
            tool_call_input_template是根据parameters字段构造的工具的输入参数,这是你最终要输出的格式！！！\n"""
            # runtime = {
            #     "tenant_id": tenant_id,
            #     "credentials": {},
            #     "invoke_from": invoke_from,
            #     "tool_invoke_from": tool_invoke_from,
            #     "app_id": app_id,
            # }
            prompt = (
                tools_introduce
                + """当用户输入一个query的时候，你需要根据query（也即问题）选择工具，输出工具名称和工具的输入参数，每个工具的格式描述就是tool_call_input_template字段，输出必须以json形式呈现
                可以输出多个工具，最终格式如下：
                {"tools":[$tool_call_input_template,$tool_call_input_template,...]}
                具体表现为：{"tools":[{"tool_name":$tool_name,"tool_input":$tool_input},{"tool_name":$tool_name,"tool_input":$tool_input}]}
                这里的tool_input就是根据工具描述中的paramters字段来构造的，例如search_from_internet工具的描述如下：
                {"name": "search_from_internet", "label": "联网搜索工具", "description": {"en_us": "", "zh_hans": "", "pt_br": "", "ja_jp": ""}, "parameters": [{"name": "query", "description": "", "type": "string"}]}
                因此tool_input就是{"query":"用户的query转化为工具的输入参数"}
            示例：
            input:今天的天气如何?
            output:{"tools":[{"tool_name":"search_from_internet","tool_input":{"query":"今天的天气"}]}
            开始吧！
            input:"""
                + query
                + "\noutput:"
            )
            llm_resp = get_adam_sys(system, prompt)
            clear_resp = llm_resp.split("</think>")[-1]
            agent_resp = []
            tools = json.loads(clear_resp)
            final_resp = ""
            for tool in tools["tools"]:
                tool_instance = tool_instances[tool["tool_name"]]
                tool_instance.runtime = Tool.Runtime(
                    **{"invoke_from": InvokeFrom.DEBUGGER}
                )
                meta, response_list = ToolEngine._invoke(
                    tool_instance, tool["tool_input"], end_user.id
                )
                response_list = tool_instance.invoke(end_user.id, tool["tool_input"])
                clear_tool_resp = parse_tool_resp(response_list)
                logging.error(f"[liweizheng] response: {clear_tool_resp}")
                agent_resp.append(clear_tool_resp)

            return {"result": agent_resp}

        except ProviderTokenNotInitError as ex:
            raise ProviderNotInitializeError(ex.description)
        except QuotaExceededError:
            raise ProviderQuotaExceededError()
        except ModelCurrentlyNotSupportError:
            raise ProviderModelCurrentlyNotSupportError()
        except InvokeError as e:
            raise CompletionRequestError(e.description)
        except (ValueError, AppInvokeQuotaExceededError) as e:
            raise e
        except Exception as e:
            logging.exception(f"internal server error. e: {repr(e)}")
            raise InternalServerError()


api.add_resource(ToolRunApi, "/tool/run")
api.add_resource(AgentRunApi, "/agent/run")
