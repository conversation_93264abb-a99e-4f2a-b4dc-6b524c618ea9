import re
import time
from extensions.ext_storage import storage
from controllers.service_api import api
import logging
from flask import request, Response
from flask_restful import Resource, reqparse
import json
import base64
import imghdr
import magic
from svrkit_core import oss


# 从cos拉到原始数据
def get_origin_data_from_cos(cos_storage_id):
    logging.info(
        f"CosImageFileDownloadApi download_file cos_storage_id {cos_storage_id}"
    )
    before_req_time = int(time.time() * 1000)
    data = storage.load_once(cos_storage_id)
    after_req_time = int(time.time() * 1000)
    logging.info(f"CosImageFileDownloadApi image download download_file time cost {after_req_time - before_req_time}")
    return data

# 获取单个文件的base64
def get_res_base64_single(cos_storage_id):
    data = get_origin_data_from_cos(cos_storage_id)
    if not data:
        return ""
    origin_data = json.loads(data)
    msgs = origin_data.get('request', {}).get('messages', [])
    image_base64 = ""
    for msg_meta in msgs:
        if msg_meta.get('role') != 'user':
            continue
        content = msg_meta.get('content', [])
        for meta_item in content:
            if meta_item.get('type') == 'image_url':
                image_base64 = meta_item.get('image_url', {}).get('url', '')
                oss.OssAttrInc(529811, 45, 1)
                return image_base64
    return image_base64


# 推断文件类型
def detect_image_type(binary_data):
    try:
        mime = magic.Magic(mime=True)
        file_type = mime.from_buffer(binary_data)
        return file_type if file_type.startswith('image/') else None
    except ImportError:
        # 备用检测方案
        from imghdr import what
        return what(None, h=binary_data)

# 单文件base64处理成图片
def get_file_from_base64_source(cos_storage_id):
    base64_source = get_res_base64_single(cos_storage_id)
    logging.info(f"base64_source = {len(base64_source)}")
    if not base64_source:
        return Response('Missing data parameter', status=400)

    # 处理数据URI前缀
    mime_type = None
    append_base64_source = ""
    if base64_source.startswith('data:'):
        match = re.match(r'data:(image/[^;]+);base64,(.*)', base64_source, re.DOTALL)
        if match:
            mime_type, append_base64_source = match.groups()
            logging.info(f"mime_type = {mime_type}, append_base64_source = {len(append_base64_source)}")
        else:
            return Response('Invalid data URI format', status=400)
    
    # Base64解码
    try:
        binary_data = base64.b64decode(append_base64_source, validate=True)
    except Exception as e:
        oss.OssAttrInc(529811, 46, 1)
        return Response(f'Invalid Base64: {str(e)}', status=400)
    
    # 验证图片格式
    detected_type = detect_image_type(binary_data)
    logging.info(f"detected_type = {detected_type}")
    if not detected_type:
        oss.OssAttrInc(529811, 47, 1)
        return Response('Invalid image format', status=400)
    
    # 确定MIME类型
    final_mime = mime_type or detected_type
    logging.info(f"final_mime = {final_mime}")
    
    # 返回响应
    response = Response(binary_data, content_type=final_mime)
    
    # 设置缓存头（默认1小时）
    cache_time = request.args.get('cache', '3600')
    response.headers['Cache-Control'] = f'max-age={cache_time}, public'
    logging.info(f"response.headers['Cache-Control'] = {response.headers['Cache-Control']}")
    return response


class CosImageFileDownloadApi(Resource):
    def get(self):
        parser = reqparse.RequestParser()
        parser.add_argument("cos_storage_id", type=str, location="args")
        args = parser.parse_args()
        cos_storage_id = args.get("cos_storage_id")
        oss.OssAttrInc(529811, 42, 1)
        logging.info(f"CosImageFileDownloadApi called {cos_storage_id}")
        try:
            data = get_file_from_base64_source(cos_storage_id)
            oss.OssAttrInc(529811, 43, 1)
            return data
        except Exception as e:
            oss.OssAttrInc(529811, 44, 1)
            logging.error(f"CosImageFileDownloadApi called {cos_storage_id} error {e}")
            return {"status": "error", "error": str(e)}

api.add_resource(CosImageFileDownloadApi, "/image/download/cos")