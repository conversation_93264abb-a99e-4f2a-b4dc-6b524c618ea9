{
    "page": 3,
    "limit": 10,
    "total": 644,
    "has_more": true,
    "data": [
        {
            "id": "3025aaaa-aadd-4204-949a-2e0751418d92",
            "workflow_run": {
                "id": "bf38cdd4-8511-453c-8f69-df56c241ef79",
                "version": "2025-02-22 03:57:44.954098",
                "status": "succeeded",
                "error": null,
                "elapsed_time": 6.***************,
                "total_tokens": 628,
                "total_steps": 5,
                "created_at": **********,
                "finished_at": **********,
                "exceptions_count": 0
            },
            "created_from": "service-api",
            "created_by_role": "end_user",
            "created_by_account": null,
            "created_by_end_user": {
                "id": "437cbeaf-1cc6-4870-ba29-9568a0da8f41",
                "type": "service_api",
                "is_anonymous": false,
                "session_id": "chatroom_50610468946-uin_100022-openid_oZpdM7ZUyUigH7Dqa67XXsOP38Pk/排骨/ 我是 andy，负责 微信iOS 平台"
            },
            "created_at": **********
        },
        {
            "id": "bbd97dc9-cfc7-4abb-9523-194a91165e33",
            "workflow_run": {
                "id": "61b54cda-581c-4652-8f8c-b6c13672d8d2",
                "version": "2025-02-22 03:57:44.954098",
                "status": "succeeded",
                "error": null,
                "elapsed_time": 11.***************,
                "total_tokens": 538,
                "total_steps": 5,
                "created_at": **********,
                "finished_at": **********,
                "exceptions_count": 0
            },
            "created_from": "service-api",
            "created_by_role": "end_user",
            "created_by_account": null,
            "created_by_end_user": {
                "id": "633105ab-ca50-41a8-b043-ddc2fe92876f",
                "type": "service_api",
                "is_anonymous": false,
                "session_id": "chatroom_50610468946-uin_100008-openid_oZpdM7dpfoL3UjzKGRfr7aNxvtVk/kirozhao(赵原)/ 向大家介绍下你自己"
            },
            "created_at": **********
        },
        {
            "id": "365ab17b-624b-42f4-9e79-615a51600eb6",
            "workflow_run": {
                "id": "d32b0324-1abb-4b28-9c42-7a745f76b880",
                "version": "2025-02-22 03:57:44.954098",
                "status": "succeeded",
                "error": null,
                "elapsed_time": 97.**************,
                "total_tokens": 62043,
                "total_steps": 5,
                "created_at": **********,
                "finished_at": **********,
                "exceptions_count": 0
            },
            "created_from": "service-api",
            "created_by_role": "end_user",
            "created_by_account": null,
            "created_by_end_user": {
                "id": "f259b952-99b0-4806-b640-f5ec22311bb0",
                "type": "service_api",
                "is_anonymous": false,
                "session_id": "chatroom_56719892294-uin_100008-openid_oZpdM7dpfoL3UjzKGRfr7aNxvtVk/kiro/ 未来你被大规模提供给用户后，如何避免我"
            },
            "created_at": **********
        },
        {
            "id": "05af77ca-8c7f-4d27-8796-6bea94f623ce",
            "workflow_run": {
                "id": "a82f2052-9a5a-4761-ad52-0ae1a4feab5d",
                "version": "2025-02-22 03:57:44.954098",
                "status": "succeeded",
                "error": null,
                "elapsed_time": 28.***************,
                "total_tokens": 9104,
                "total_steps": 5,
                "created_at": **********,
                "finished_at": **********,
                "exceptions_count": 0
            },
            "created_from": "service-api",
            "created_by_role": "end_user",
            "created_by_account": null,
            "created_by_end_user": {
                "id": "cb6e7388-6f5c-4281-a1b8-b8bb0009ff50",
                "type": "service_api",
                "is_anonymous": false,
                "session_id": "chatroom_42986426804-uin_1343591425-openid_oZpdM7f5hC6-UXGlO7UpYfXLKnUo/鄞沛夫#Nomo/ 重新根据你的漏检逻辑，看下还有其他什么"
            },
            "created_at": **********
        },
        {
            "id": "acc3bd84-81c4-4a2d-a6c7-41e4ae2119a3",
            "workflow_run": {
                "id": "7e111a6f-97ba-42b3-863d-819a60d71ecb",
                "version": "2025-02-22 03:57:44.954098",
                "status": "succeeded",
                "error": null,
                "elapsed_time": 25.**************,
                "total_tokens": 8733,
                "total_steps": 5,
                "created_at": **********,
                "finished_at": **********,
                "exceptions_count": 0
            },
            "created_from": "service-api",
            "created_by_role": "end_user",
            "created_by_account": null,
            "created_by_end_user": {
                "id": "66411e1b-f346-4f1c-aca1-6f8b471e7470",
                "type": "service_api",
                "is_anonymous": false,
                "session_id": "chatroom_42986426804-uin_1343591425-openid_oZpdM7f5hC6-UXGlO7UpYfXLKnUo/鄞沛夫#Nomo/：TIT水果群今天很夸张，10:15 分"
            },
            "created_at": **********
        },
        {
            "id": "5df49f17-5078-4df3-a297-6ea867b4f0d3",
            "workflow_run": {
                "id": "********-1498-48d8-a37c-2c8ffdf87d39",
                "version": "2025-02-22 03:57:44.954098",
                "status": "succeeded",
                "error": null,
                "elapsed_time": 25.***************,
                "total_tokens": 8407,
                "total_steps": 5,
                "created_at": **********,
                "finished_at": **********,
                "exceptions_count": 0
            },
            "created_from": "service-api",
            "created_by_role": "end_user",
            "created_by_account": null,
            "created_by_end_user": {
                "id": "0de99e0b-0ab4-4667-a07e-e5632f2485f6",
                "type": "service_api",
                "is_anonymous": false,
                "session_id": "chatroom_42986426804-uin_1343591425-openid_oZpdM7f5hC6-UXGlO7UpYfXLKnUo/鄞沛夫#Nomo/ 为什么没有统计周五中我的数据"
            },
            "created_at": **********
        },
        {
            "id": "237e99d2-c951-4e85-862e-58e3c0dad0f2",
            "workflow_run": {
                "id": "********-0794-45bd-9670-cabb82fab08c",
                "version": "2025-02-22 03:57:44.954098",
                "status": "succeeded",
                "error": null,
                "elapsed_time": 25.**************,
                "total_tokens": 8136,
                "total_steps": 5,
                "created_at": **********,
                "finished_at": **********,
                "exceptions_count": 0
            },
            "created_from": "service-api",
            "created_by_role": "end_user",
            "created_by_account": null,
            "created_by_end_user": {
                "id": "21764aae-acc6-4e65-adbe-1d00b0c4d387",
                "type": "service_api",
                "is_anonymous": false,
                "session_id": "chatroom_42986426804-uin_1343591425-openid_oZpdM7f5hC6-UXGlO7UpYfXLKnUo/鄞沛夫#Nomo/ 让你统计上周，为什么统计了 24 号"
            },
            "created_at": **********
        },
        {
            "id": "4835bebd-fd84-45dc-956b-640d2e15fa4b",
            "workflow_run": {
                "id": "0e8ca7da-79a2-4839-92f8-3769829b9ed2",
                "version": "2025-02-22 03:57:44.954098",
                "status": "succeeded",
                "error": null,
                "elapsed_time": 18.***************,
                "total_tokens": 7798,
                "total_steps": 5,
                "created_at": **********,
                "finished_at": **********,
                "exceptions_count": 0
            },
            "created_from": "service-api",
            "created_by_role": "end_user",
            "created_by_account": null,
            "created_by_end_user": {
                "id": "021cc458-3c63-46a0-8a8c-e2aef3cefcb6",
                "type": "service_api",
                "is_anonymous": false,
                "session_id": "chatroom_42986426804-uin_1343591425-openid_oZpdM7f5hC6-UXGlO7UpYfXLKnUo/鄞沛夫#Nomo/ 总结下上周，在群里用冒号开头发消息的消"
            },
            "created_at": **********
        },
        {
            "id": "e2c06b43-140e-4815-8ba5-56cd61850514",
            "workflow_run": {
                "id": "9adf6259-564e-4926-89ce-c62b4004100c",
                "version": "2025-02-22 03:57:44.954098",
                "status": "succeeded",
                "error": null,
                "elapsed_time": 28.**************,
                "total_tokens": 7586,
                "total_steps": 5,
                "created_at": **********,
                "finished_at": **********,
                "exceptions_count": 0
            },
            "created_from": "service-api",
            "created_by_role": "end_user",
            "created_by_account": null,
            "created_by_end_user": {
                "id": "c062fcc2-5649-4802-b371-74751fa57019",
                "type": "service_api",
                "is_anonymous": false,
                "session_id": "chatroom_42986426804-uin_1343591425-openid_oZpdM7f5hC6-UXGlO7UpYfXLKnUo/鄞沛夫#Nomo/ 

我们想建立一个小组学习机制，有两个"
            },
            "created_at": **********
        },
        {
            "id": "fd525890-9657-4198-aea8-1e8b3793f970",
            "workflow_run": {
                "id": "7ca5d981-a0fb-4610-9815-323fc8c5065e",
                "version": "2025-02-22 03:57:44.954098",
                "status": "succeeded",
                "error": null,
                "elapsed_time": 23.**************,
                "total_tokens": 7230,
                "total_steps": 5,
                "created_at": 1*********,
                "finished_at": **********,
                "exceptions_count": 0
            },
            "created_from": "service-api",
            "created_by_role": "end_user",
            "created_by_account": null,
            "created_by_end_user": {
                "id": "0ac58eb2-71e9-4fc0-8f5a-b39fd77cbb30",
                "type": "service_api",
                "is_anonymous": false,
                "session_id": "chatroom_42986426804-uin_2106746461-openid_oZpdM7WAF1na30pCtgH-6hDe1k9k/Sakura/ 老大老二老三老四分别是谁"
            },
            "created_at": **********
        }
    ]
}