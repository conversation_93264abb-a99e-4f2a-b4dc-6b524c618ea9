from flask import request, Response
from flask_restful import Resource, reqparse
import logging

import base64
from controllers.service_api import api
from core.helper import ssrf_proxy


class ImageFileDownloadApi(Resource):
    def post(self):
        # 参数解析
        parser = reqparse.RequestParser()
        parser.add_argument(
            "url", type=str, required=True, help="Image URL is required"
        )
        args = parser.parse_args()
        image_url = args.get("url")
        logging.error(f"[liweizheng] ImageFileDownloadApi called url {image_url}")

        if not image_url:
            return {"error": "Missing url parameter"}, 400

        try:
            resp = ssrf_proxy.get(url=image_url, timeout=5, follow_redirects=True)
            resp.raise_for_status()

            content_type = resp.headers.get("Content-Type", "application/octet-stream")
            # 返回图片字节流
            return Response(resp.content, content_type=content_type)

        except Exception as e:
            return {"error": f"Failed to download image: {str(e)}"}, 500
