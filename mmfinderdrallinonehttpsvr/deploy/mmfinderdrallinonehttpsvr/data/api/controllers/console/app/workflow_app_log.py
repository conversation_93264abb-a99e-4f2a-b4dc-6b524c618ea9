import logging
import re
from flask import g
from flask_restful import Resource, marshal_with, reqparse
from flask_restful.inputs import int_range

from controllers.console import api
from controllers.console.app.wraps import get_app_model
from controllers.console.wraps import account_initialization_required, setup_required
from fields.workflow_app_log_fields import workflow_app_log_pagination_fields
from libs.login import login_required
from models import App
from models.model import AppMode
from services.workflow_app_service import WorkflowAppService
from flask_sqlalchemy.pagination import SelectPagination
from wx.rainbow_conf import get_rainbow_kv_conf


class WorkflowAppLogApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    @get_app_model(mode=[AppMode.WORKFLOW])
    @marshal_with(workflow_app_log_pagination_fields)
    def get(self, app_model: App):
        """
        Get workflow app logs
        """
        parser = reqparse.RequestParser()
        parser.add_argument("keyword", type=str, location="args")
        parser.add_argument("status", type=str, choices=["succeeded", "failed", "stopped"], location="args")
        parser.add_argument("page", type=int_range(1, 99999), default=1, location="args")
        parser.add_argument("limit", type=int_range(1, 100), default=20, location="args")
        args = parser.parse_args()

        # get paginate workflow app logs
        workflow_app_service = WorkflowAppService()
        workflow_app_log_pagination : SelectPagination= workflow_app_service.get_paginate_workflow_app_logs(
            app_model=app_model, args=args
        )

        is_super_user: bool = isinstance(g.extend_info, dict) and ('rtx' in g.extend_info) and (g.extend_info['rtx']!='') and (
            g.extend_info['rtx'] in get_rainbow_kv_conf(group="dify.system",key="boss_chat_history_admin", default_="kristendi"))

        
        logging.info(f"superuser {is_super_user} . {type(workflow_app_log_pagination.items)}")

        new_lst=list()
        for idx,log in enumerate(workflow_app_log_pagination.items):
            #  "chatroom_50610468946-uin_100008-openid_oZpdM7dpfoL3UjzKGRfr7aNxvtVk/kirozhao(赵原)/ 向大家介绍下你自己"
            if re.findall(r"(?:(?:uin_(?:100007|113612|100011|100008|195704775))|chatroom_50610468946)", log.created_by_end_user.session_id):
                if not is_super_user:
                    logging.info(f"ignore one no.{idx} row")
                    continue
            new_lst.append(log)
        workflow_app_log_pagination.items=new_lst

        return workflow_app_log_pagination


api.add_resource(WorkflowAppLogApi, "/apps/<uuid:app_id>/workflow-app-logs")
