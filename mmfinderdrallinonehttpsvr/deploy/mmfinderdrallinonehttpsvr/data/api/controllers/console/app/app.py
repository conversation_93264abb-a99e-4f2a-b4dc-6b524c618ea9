import uuid
from typing import cast
import json
import re
from concurrent.futures import ThreadPoolExecutor
from services.tag_service import TagService

import random
from flask import g
from flask_login import current_user
from flask_restful import Resource, inputs, marshal, marshal_with, reqparse
from sqlalchemy import select
from sqlalchemy.orm import Session
from werkzeug.exceptions import BadRequest, Forbidden, abort

from controllers.console import api
from controllers.console.app.wraps import get_app_model
from controllers.console.wraps import (
    account_initialization_required,
    cloud_edition_billing_resource_check,
    enterprise_license_required,
    setup_required,
)
from core.ops.ops_trace_manager import OpsTraceManager
from extensions.ext_database import db
from fields.app_fields import (
    app_detail_fields,
    app_detail_fields_with_site,
    app_pagination_fields,
    app_partial_fields
)
from wx.rainbow_conf import get_admin_rtx_list
from libs.login import login_required
from models import Account, App
from services.app_dsl_service import AppDslService, ImportMode
from services.app_service import AppService
import monitor
from datetime import datetime,date
import logging
import os
from wx.dao import IsLocalConfigDAO
import local_config
from models.workflow import Workflow

ALLOW_CREATE_APP_MODES = ["chat", "agent-chat", "advanced-chat", "workflow", "completion"]

def paginate(data: list, page: int, limit: int) -> list:
    if not data or page < 1 or limit < 1:
        return []
    
    start = (page - 1) * limit
    if start >= len(data):
        return []  # 超出数据范围，返回空列表
    
    end = start + limit
    return data[start:end]

def get_app_list_from_local(args):
    all_apps=local_config.GetAll(App)
    logging.info(f"[kris] args:{repr(args)} len(all_apps):{len(all_apps)}")

    sorted_list = sorted(all_apps, key=lambda x: x.updated_at,
                         reverse=True)
    if args.get('mode') and args['mode']!='all':
        mode=args['mode']
        sorted_list=[i for i in sorted_list if i.mode==mode] 
    if args.get("name"):
        name = args["name"]
        uuid_pattern = re.compile(
            r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$',
            re.IGNORECASE
        )
        if uuid_pattern.match(name):
            sublist = [i for i in sorted_list if i.id == name]
        else:
            sublist = [i for i in sorted_list if (name in i.name)]
    else:
        sublist = sorted_list
    
    limit = args['limit']
    page = args['page']
    final_list = paginate(sublist, page, limit)
    def dump(i):
        tmp = marshal(i, app_partial_fields)
        if i.mode == 'workflow':
            try:
                updated_at = datetime.fromtimestamp(
                    tmp['updated_at']).strftime('%Y-%m-%d %H:%M:%S')
                # todo(kris): 效率问题,不过白板不 care...
                workflow: Workflow = local_config.GetOneByCustomKey(
                    Workflow, lambda x: x.id == i.workflow_id)
                rtx = '' if workflow is None else workflow.rtx
                tmp['description'] = f"{rtx} {updated_at} | " + \
                    tmp['description']
            except Exception as e:
                pass
        return tmp
    return {"data": [dump(i) for i in final_list], "total": len(sublist), "page": page, "limit": limit, "has_more": page*limit < len(sublist)}

class AppListApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    @enterprise_license_required
    def get(self):
        """Get app list"""

        def uuid_list(value):
            try:
                return [str(uuid.UUID(v)) for v in value.split(",")]
            except ValueError:
                abort(400, message="Invalid UUID format in tag_ids.")

        parser = reqparse.RequestParser()
        parser.add_argument("page", type=inputs.int_range(1, 99999), required=False, default=1, location="args")
        parser.add_argument("limit", type=inputs.int_range(1, 100), required=False, default=20, location="args")
        parser.add_argument(
            "mode",
            type=str,
            choices=["chat", "workflow", "agent-chat", "channel", "all"],
            default="all",
            location="args",
            required=False,
        )
        parser.add_argument("name", type=str, location="args", required=False)
        parser.add_argument("tag_ids", type=uuid_list, location="args", required=False)

        args = parser.parse_args()

        if IsLocalConfigDAO():
            logging.info(f"[kris] local_dao_app_list {repr(args)}")
            return get_app_list_from_local(args)

        # get app list
        app_service = AppService()
        app_pagination = app_service.get_paginate_apps(current_user.current_tenant_id, args)
        if not app_pagination:
            return {"data": [], "total": 0, "page": 1, "limit": 20, "has_more": False}

        monitor.timelog('query db complete')

        ret = marshal(app_pagination, app_pagination_fields)
        monitor.timelog('marshal complete')

        if date.today().strftime('%m-%d') in ['03-31','04-01']:
            for it in ret['data']:
                it['icon']=random.choice(['🔫','🧸','🌸'])
                it['name'] = f"{random.choice(['🍇', '🍉', '🍊', '🍋', '🍌', '🍍', 
                '🥭', '🍎', '🍏', '🍐', '🍑', '🍒', '🍓', '🫐', '🥝', '🍅', '🫒', '🥥', '🍋‍🟩', '🥑',])} {it['name']}"
        return ret

    @setup_required
    @login_required
    @account_initialization_required
    @marshal_with(app_detail_fields)
    @cloud_edition_billing_resource_check("apps")
    def post(self):
        if os.environ.get('READONLY', 'False') == 'True':
            raise Forbidden()

        """Create app"""
        parser = reqparse.RequestParser()
        parser.add_argument("name", type=str, required=True, location="json")
        parser.add_argument("description", type=str, location="json")
        parser.add_argument("mode", type=str, choices=ALLOW_CREATE_APP_MODES, location="json")
        parser.add_argument("icon_type", type=str, location="json")
        parser.add_argument("icon", type=str, location="json")
        parser.add_argument("icon_background", type=str, location="json")
        args = parser.parse_args()

        # The role of the current user in the ta table must be admin, owner, or editor
        if not current_user.is_editor:
            raise Forbidden()

        if "mode" not in args or args["mode"] is None:
            raise BadRequest("mode is required")

        app_service = AppService()
        app = app_service.create_app(current_user.current_tenant_id, args, current_user)

        try:
            user = g.extend_info.get('rtx', '')
            if user=='':
                return app, 201

            tags = TagService.get_tags("app", 
                                       current_user.current_tenant_id, 
                                       user)
            if len(tags) == 0:
                tag = TagService.save_tags({
                    "type": "app",
                    "name": user,
                })
            else:
                tag = tags[0]

            TagService.save_tag_binding({
                "tag_ids": [ tag.id ],
                "target_id": app.id,
                "type": "app",
            })
            logging.error(f"user:{user} tag:{tag} create app: {app}")
        except Exception as e:
            pass

        return app, 201


class AppApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    @enterprise_license_required
    @get_app_model
    @marshal_with(app_detail_fields_with_site)
    def get(self, app_model):
        """Get app detail"""
        app_service = AppService()

        monitor.timelog(f"start get app")

        app_model = app_service.get_app(app_model)

        monitor.timelog(f"service get app complete")

        return app_model

    @setup_required
    @login_required
    @account_initialization_required
    @get_app_model
    @marshal_with(app_detail_fields_with_site)
    def put(self, app_model):
        if os.environ.get('READONLY', 'False') == 'True':
            raise Forbidden()

        """Update app"""
        # The role of the current user in the ta table must be admin, owner, or editor
        if not current_user.is_editor:
            raise Forbidden()

        parser = reqparse.RequestParser()
        parser.add_argument("name", type=str, required=True, nullable=False, location="json")
        parser.add_argument("description", type=str, location="json")
        parser.add_argument("icon_type", type=str, location="json")
        parser.add_argument("icon", type=str, location="json")
        parser.add_argument("icon_background", type=str, location="json")
        parser.add_argument("max_active_requests", type=int, location="json")
        parser.add_argument("use_icon_as_answer_icon", type=bool, location="json")
        args = parser.parse_args()

        app_service = AppService()
        app_model = app_service.update_app(app_model, args)

        return app_model

    @setup_required
    @login_required
    @account_initialization_required
    @get_app_model
    def delete(self, app_model):
        if os.environ.get('READONLY', 'False') == 'True':
            raise Forbidden()

        """Delete app"""
        # The role of the current user in the ta table must be admin, owner, or editor
        if not current_user.is_editor:
            raise Forbidden()

        rtx=g.extend_info.get('rtx', '')
        app_id=g.extend_info.get('app_id', '')

        rainbow_admin_rtxs=get_admin_rtx_list(app_id=app_id)
        logging.error(f"[kris][publish] rtx:'{rtx}' app_id:'{app_id}' rar:'{repr(rainbow_admin_rtxs)}'")
        if (rainbow_admin_rtxs is not None) and (rtx not in rainbow_admin_rtxs):
            e=Forbidden()
            e.description=f"""用户 '{rtx}' 不在该 dify-app({app_id}) 的管理员白名单({repr(rainbow_admin_rtxs)}) 内,禁止修改.详情联系 kristendi. 
https://rainbow.woa.com/console/b315e2e5-40b2-4ac0-b724-a61fd2c2cfa3/Default/list?group_id=7798507&group_name=dify.app_admin&tab=config """
            raise e

        app_service = AppService()
        app_service.delete_app(app_model)

        return {"result": "success"}, 204


class AppCopyApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    @get_app_model
    @marshal_with(app_detail_fields_with_site)
    def post(self, app_model):
        if os.environ.get('READONLY', 'False') == 'True':
            raise Forbidden()

        """Copy app"""
        # The role of the current user in the ta table must be admin, owner, or editor
        if not current_user.is_editor:
            raise Forbidden()

        parser = reqparse.RequestParser()
        parser.add_argument("name", type=str, location="json")
        parser.add_argument("description", type=str, location="json")
        parser.add_argument("icon_type", type=str, location="json")
        parser.add_argument("icon", type=str, location="json")
        parser.add_argument("icon_background", type=str, location="json")
        args = parser.parse_args()

        with Session(db.engine) as session:
            import_service = AppDslService(session)
            yaml_content = import_service.export_dsl(app_model=app_model, include_secret=True)
            account = cast(Account, current_user)
            result = import_service.import_app(
                account=account,
                import_mode=ImportMode.YAML_CONTENT.value,
                yaml_content=yaml_content,
                name=args.get("name"),
                description=args.get("description"),
                icon_type=args.get("icon_type"),
                icon=args.get("icon"),
                icon_background=args.get("icon_background"),
            )
            session.commit()

            stmt = select(App).where(App.id == result.app_id)
            app = session.scalar(stmt)
            if app is None:
                return app, 201

            try:
                user = g.extend_info.get('rtx', '')
                if user=='':
                    return app, 201

                tags = TagService.get_tags("app", 
                                           current_user.current_tenant_id, 
                                           user)
                if len(tags) == 0:
                    tag = TagService.save_tags({
                        "type": "app",
                        "name": user,
                    })
                else:
                    tag = tags[0]

                TagService.save_tag_binding({
                    "tag_ids": [ tag.id ],
                    "target_id": app.id,
                    "type": "app",
                })

                logging.error(f"user:{user} tag:{tag} copy app: {app}")
            except Exception as e:
                pass

        return app, 201


class AppExportApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    @get_app_model
    def get(self, app_model):
        """Export app"""
        # The role of the current user in the ta table must be admin, owner, or editor
        if not current_user.is_editor:
            raise Forbidden()

        # Add include_secret params
        parser = reqparse.RequestParser()
        parser.add_argument("include_secret", type=inputs.boolean, default=False, location="args")
        args = parser.parse_args()

        return {"data": AppDslService.export_dsl(app_model=app_model, include_secret=args["include_secret"])}


class AppNameApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    @get_app_model
    @marshal_with(app_detail_fields)
    def post(self, app_model):
        if os.environ.get('READONLY', 'False') == 'True':
            raise Forbidden()

        # The role of the current user in the ta table must be admin, owner, or editor
        if not current_user.is_editor:
            raise Forbidden()

        parser = reqparse.RequestParser()
        parser.add_argument("name", type=str, required=True, location="json")
        args = parser.parse_args()

        app_service = AppService()
        app_model = app_service.update_app_name(app_model, args.get("name"))

        return app_model


class AppIconApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    @get_app_model
    @marshal_with(app_detail_fields)
    def post(self, app_model):
        if os.environ.get('READONLY', 'False') == 'True':
            raise Forbidden()

        # The role of the current user in the ta table must be admin, owner, or editor
        if not current_user.is_editor:
            raise Forbidden()

        parser = reqparse.RequestParser()
        parser.add_argument("icon", type=str, location="json")
        parser.add_argument("icon_background", type=str, location="json")
        args = parser.parse_args()

        app_service = AppService()
        app_model = app_service.update_app_icon(app_model, args.get("icon"), args.get("icon_background"))

        return app_model


class AppSiteStatus(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    @get_app_model
    @marshal_with(app_detail_fields)
    def post(self, app_model):
        if os.environ.get('READONLY', 'False') == 'True':
            raise Forbidden()

        # The role of the current user in the ta table must be admin, owner, or editor
        if not current_user.is_editor:
            raise Forbidden()

        parser = reqparse.RequestParser()
        parser.add_argument("enable_site", type=bool, required=True, location="json")
        args = parser.parse_args()

        app_service = AppService()
        app_model = app_service.update_app_site_status(app_model, args.get("enable_site"))

        return app_model


class AppApiStatus(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    @get_app_model
    @marshal_with(app_detail_fields)
    def post(self, app_model):
        if os.environ.get('READONLY', 'False') == 'True':
            raise Forbidden()

        # The role of the current user in the ta table must be admin or owner
        if not current_user.is_admin_or_owner:
            raise Forbidden()

        parser = reqparse.RequestParser()
        parser.add_argument("enable_api", type=bool, required=True, location="json")
        args = parser.parse_args()

        app_service = AppService()
        app_model = app_service.update_app_api_status(app_model, args.get("enable_api"))

        return app_model


class AppTraceApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    def get(self, app_id):
        """Get app trace"""
        app_trace_config = OpsTraceManager.get_app_tracing_config(app_id=app_id)

        return app_trace_config

    @setup_required
    @login_required
    @account_initialization_required
    def post(self, app_id):
        if os.environ.get('READONLY', 'False') == 'True':
            raise Forbidden()

        # add app trace
        if not current_user.is_admin_or_owner:
            raise Forbidden()
        parser = reqparse.RequestParser()
        parser.add_argument("enabled", type=bool, required=True, location="json")
        parser.add_argument("tracing_provider", type=str, required=True, location="json")
        args = parser.parse_args()

        OpsTraceManager.update_app_tracing_config(
            app_id=app_id,
            enabled=args["enabled"],
            tracing_provider=args["tracing_provider"],
        )

        return {"result": "success"}


class AppDeprecatedModifyApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    def post(self):
        if os.environ.get('READONLY', 'False') == 'True':
            raise Forbidden()

        app_service = AppService()
        parser = reqparse.RequestParser()
        parser.add_argument("day", type=int, required=False, location="json")
        args = parser.parse_args()
        app_service.modify_deprecated_apps(args)
        return {"result": "success"}
    
class AppAgentListApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    def get(self):
        app_service = AppService()
        parser = reqparse.RequestParser()
        args = parser.parse_args()
        agent_list = app_service.get_agent_list(tenant_id=current_user.current_tenant_id)

        data = []
        for agent in agent_list:
            data.append({'agent_id': agent.id, 'agent_name': agent.name, 'agent_description': agent.description, 'conversation_list': []})

        for d in data:
            conversation_list = app_service.get_conversation_list(app_id=d['agent_id'])
            for conversation in conversation_list:
                d['conversation_list'].append({'conversation_id': conversation.id, 'conversation_name': conversation.name, 'history_messages': []})

        def unicode_to_chinese(unicode_str):
            """将包含代理对和 Unicode 转义序列的字符串转换为中文字符"""
            # 第一步：处理代理对（如 \uD83D\uDE00）
            def replace_surrogate(match):
                high, low = match.groups()
                codepoint = (int(high, 16) - 0xD800) * 0x400 + (int(low, 16) - 0xDC00) + 0x10000
                return chr(codepoint)
            
            # 匹配代理对（如 \uD83D\uDE00）
            surrogate_pattern = re.compile(r'\\u(d[89ab][0-9a-f]{2})\\u(d[cdef][0-9a-f]{2})', re.IGNORECASE)
            processed_str = surrogate_pattern.sub(replace_surrogate, unicode_str)
            
            # 第二步：处理普通 Unicode 转义（如 \u4F60 或 \U0001F600）
            def replace_unicode_escape(match):
                hex_str = match.group(1)
                return chr(int(hex_str, 16))
            
            # 处理 \UXXXXXXXX 8位转义
            processed_str = re.sub(r'\\U([0-9a-fA-F]{8})', replace_unicode_escape, processed_str)
            # 处理 \uXXXX 4位转义
            processed_str = re.sub(r'\\u([0-9a-fA-F]{4})', replace_unicode_escape, processed_str)
            
            return processed_str

        for d in data:
            for conv in d['conversation_list']:
                history_messages = app_service.get_history_messages(conversation_id=conv['conversation_id'])
                conv_history_messages = []
                if len(history_messages) > 0:
                    conv_history_messages = history_messages[0].message
                    if history_messages[0].answer:
                        conv_history_messages.append({'role': 'assistant', 'text': history_messages[0].answer})
                
                for msg in conv_history_messages:
                    if (msg['role'] == 'assistant' or msg['role'] == 'tool') and 'tool_calls' in msg:
                        for func in msg['tool_calls']:
                            func['function']['arguments'] = unicode_to_chinese(func['function']['arguments'])

                conv['history_messages'] = conv_history_messages
        
        with open("data2.json", "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=4)

        '''
        for agent in agent_list:
            print('hhhh: ', agent.id, agent.name)
        # "app_id"
        app_id = "99c1674c-e2e2-4f61-aaee-35c57daa78c3" 
        conversation_list = app_service.get_conversation_list(app_id=app_id)
        for conversation in conversation_list:
            print('cccc: ', conversation.id, conversation.name)

        # "conversation_id"
        # ca6f64c7-edce-4b3a-be34-27ff5e780b90
        conv_id = "ca6f64c7-edce-4b3a-be34-27ff5e780b90"
        # history_messages[0]
        history_messages = app_service.get_history_messages(conversation_id=conv_id)
        chat_history = []
        if len(history_messages) > 0:
            chat_history = history_messages[0].message
            if history_messages[0].answer:
                chat_history.append({'role': 'assistant', 'test': history_messages[0].answer})
        print('chat_history: ', chat_history)
        '''
        
        return {"result": "get_agent_list, conversation_list and msg sucess"}

class AppSingleAgentListApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    def get(self, app_id):
        app_service = AppService()
        parser = reqparse.RequestParser()
        args = parser.parse_args()
        agent_list = [
            {'agent_id': app_id,
             'agent_name': '群助手，感知',
             'description': '群助手，感知'}
        ]

        data = []
        for agent in agent_list:
            data.append({'agent_id': agent['agent_id'], 'agent_name': agent['agent_name'], 'agent_description': agent['description'], 'conversation_list': []})

        for d in data:
            conversation_list = app_service.get_conversation_list(app_id=d['agent_id'])
            for conversation in conversation_list:
                d['conversation_list'].append({'conversation_id': conversation.id, 'conversation_name': conversation.name, 'history_messages': []})

        def unicode_to_chinese(unicode_str):
            """将包含代理对和 Unicode 转义序列的字符串转换为中文字符"""
            # 第一步：处理代理对（如 \uD83D\uDE00）
            def replace_surrogate(match):
                high, low = match.groups()
                codepoint = (int(high, 16) - 0xD800) * 0x400 + (int(low, 16) - 0xDC00) + 0x10000
                return chr(codepoint)
            
            # 匹配代理对（如 \uD83D\uDE00）
            surrogate_pattern = re.compile(r'\\u(d[89ab][0-9a-f]{2})\\u(d[cdef][0-9a-f]{2})', re.IGNORECASE)
            processed_str = surrogate_pattern.sub(replace_surrogate, unicode_str)
            
            # 第二步：处理普通 Unicode 转义（如 \u4F60 或 \U0001F600）
            def replace_unicode_escape(match):
                hex_str = match.group(1)
                return chr(int(hex_str, 16))
            
            # 处理 \UXXXXXXXX 8位转义
            processed_str = re.sub(r'\\U([0-9a-fA-F]{8})', replace_unicode_escape, processed_str)
            # 处理 \uXXXX 4位转义
            processed_str = re.sub(r'\\u([0-9a-fA-F]{4})', replace_unicode_escape, processed_str)
            
            return processed_str

        for d in data:
            for conv in d['conversation_list']:
                history_messages = app_service.get_history_messages(conversation_id=conv['conversation_id'])
                conv_history_messages = []
                if len(history_messages) > 0:
                    conv_history_messages = history_messages[0].message
                    if history_messages[0].answer:
                        conv_history_messages.append({'role': 'assistant', 'text': history_messages[0].answer})
                
                for msg in conv_history_messages:
                    if (msg['role'] == 'assistant' or msg['role'] == 'tool') and 'tool_calls' in msg:
                        for func in msg['tool_calls']:
                            func['function']['arguments'] = unicode_to_chinese(func['function']['arguments'])

                conv['history_messages'] = conv_history_messages
        
        with open("one_agent_data.json", "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=4)

        '''
        for agent in agent_list:
            print('hhhh: ', agent.id, agent.name)
        # "app_id"
        app_id = "99c1674c-e2e2-4f61-aaee-35c57daa78c3" 
        conversation_list = app_service.get_conversation_list(app_id=app_id)
        for conversation in conversation_list:
            print('cccc: ', conversation.id, conversation.name)

        # "conversation_id"
        # ca6f64c7-edce-4b3a-be34-27ff5e780b90
        conv_id = "ca6f64c7-edce-4b3a-be34-27ff5e780b90"
        # history_messages[0]
        history_messages = app_service.get_history_messages(conversation_id=conv_id)
        chat_history = []
        if len(history_messages) > 0:
            chat_history = history_messages[0].message
            if history_messages[0].answer:
                chat_history.append({'role': 'assistant', 'test': history_messages[0].answer})
        print('chat_history: ', chat_history)
        '''
        
        return {"result": "get_agent_list, conversation_list and msg sucess"}

class DateTimeEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()  # 将 datetime 转换为字符串
        return super().default(obj)

class AppWorkflowListApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    def get(self):
        app_service = AppService()
        parser = reqparse.RequestParser()
        args = parser.parse_args()
        # workflow_list = ['54704e94-526d-4e5d-8ce7-1cb0684dc7b3', '2192dba9-a986-4631-a903-3273d12031fb', '2f24391d-eade-48c6-8677-db2846513d9d']
        workflow_list = ['8d70508d-9226-40be-92d3-43b2b1d38fde']
        print('workflow_list: ', '*' * 100)
        data = []
        export_data = []
        for wid in workflow_list:
            workflow_logs = app_service.get_workflow_logs(tenant_id=current_user.current_tenant_id, app_id=wid)
            for work in workflow_logs:
                workflow_run = app_service.get_workflow_run_logs(tenant_id=current_user.current_tenant_id, app_id=wid, run_id=work.workflow_run_id)
                workflow_run_dict = workflow_run.to_dict()
                workflow_input = workflow_run_dict['inputs']
                workflow_output = workflow_run_dict['outputs']
                append_dict = {
                    'query': workflow_input['query'], 
                    'histoty': workflow_input['history'], 
                    'sys.user_id': workflow_input['sys.user_id'], 
                    'sys.uin': workflow_input['sys.uin'], 
                    'outputs': workflow_output.get('text', '')
                }
                export_data.append(append_dict)
                data.append({'workflow_id': wid, 'log': append_dict})
        print(data[0])
        with open("export_workflow_data.json", "w", encoding="utf-8") as f:
            json.dump(export_data, f, ensure_ascii=False, cls=DateTimeEncoder, indent=4)
            print(f"数据已成功保存到 export_workflow_data.json")
        with open("workflow_data.json", "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, cls=DateTimeEncoder, indent=4)
            print(f"数据已成功保存到 workflow_data.json")
        return {"result": "get_workflow_list sucess"}

api.add_resource(AppListApi, "/apps")
api.add_resource(AppAgentListApi, "/apps_agent_list")
api.add_resource(AppSingleAgentListApi, "/apps_single_agent")
api.add_resource(AppWorkflowListApi, "/apps_workflow_list")
api.add_resource(AppApi, "/apps/<uuid:app_id>")
api.add_resource(AppCopyApi, "/apps/<uuid:app_id>/copy")
api.add_resource(AppExportApi, "/apps/<uuid:app_id>/export")
api.add_resource(AppNameApi, "/apps/<uuid:app_id>/name")
api.add_resource(AppIconApi, "/apps/<uuid:app_id>/icon")
api.add_resource(AppSiteStatus, "/apps/<uuid:app_id>/site-enable")
api.add_resource(AppApiStatus, "/apps/<uuid:app_id>/api-enable")
api.add_resource(AppTraceApi, "/apps/<uuid:app_id>/trace")
api.add_resource(AppDeprecatedModifyApi, "/apps/modified_deprecated_apps")
