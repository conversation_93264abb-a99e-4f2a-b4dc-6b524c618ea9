import json
import os

from controllers.console.app.error import AppNotFoundError
from flask import request, g
from flask_login import current_user
from flask_restful import Resource

from controllers.console import api
from controllers.console.app.wraps import get_app_model
from controllers.console.wraps import account_initialization_required, setup_required
from core.agent.entities import AgentToolEntity
from core.tools.tool_manager import ToolManager
from core.tools.utils.configuration import ToolParameterConfigurationManager
from events.app_event import app_model_config_was_updated
from extensions.ext_database import db
from libs.login import login_required
from models.model import AppMode, AppModelConfig
from services.app_model_config_service import AppModelConfigService
from flask import g
from werkzeug.exceptions import Forbidden, InternalServerError, NotFound
from wx.rainbow_conf import get_admin_rtx_list
from wx.wxmesh_request_bridge import WxMeshSession
import logging
import wx.dao
from wx.dao.app_model_config import GetAppModelConfigById

class ModelConfigResource(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    @get_app_model(mode=[AppMode.AGENT_CHAT, AppMode.CHAT, AppMode.COMPLETION])
    def post(self, app_model):
        if os.environ.get('READONLY', 'False') == 'True':
            raise Forbidden()

        rtx=g.extend_info.get('rtx', '')
        app_id=g.extend_info.get('app_id', '')
        rainbow_admin_rtxs=get_admin_rtx_list(app_id=app_id)
        logging.error(f"[kris][publish] rtx:'{rtx}' app_id:'{app_id}' rar:'{repr(rainbow_admin_rtxs)}'")
        if (rainbow_admin_rtxs is not None) and (rtx not in rainbow_admin_rtxs):
            e=Forbidden()
            e.description=f"""用户 '{rtx}' 不在该 dify-app({app_id}) 的管理员白名单({repr(rainbow_admin_rtxs)}) 内,禁止修改.详情联系 kristendi
https://rainbow.woa.com/console/b315e2e5-40b2-4ac0-b724-a61fd2c2cfa3/Default/list?group_id=7798507&group_name=dify.app_admin&tab=config
            """
            raise e

        """Modify app model config"""
        # validate config
        model_configuration = AppModelConfigService.validate_configuration(
            tenant_id=current_user.current_tenant_id, config=request.json, app_mode=AppMode.value_of(app_model.mode)
        )

        rtx = ""
        try:
            rtx = g.extend_info.get('rtx', '')
        except Exception as e:
            logging.error(f"[nickwu] error:{e}")

        new_app_model_config = AppModelConfig(
            app_id=app_model.id,
            created_by=current_user.id,
            updated_by=current_user.id,
            rtx = rtx,
        )
        new_app_model_config = new_app_model_config.from_model_config_dict(model_configuration)

        if app_model.mode == AppMode.AGENT_CHAT.value or app_model.is_agent:
            # get original app model config
            original_app_model_config = GetAppModelConfigById(
                    app_model.app_model_config_id)
            if not original_app_model_config:
                raise AppNotFoundError()

            agent_mode = original_app_model_config.agent_mode_dict
            # decrypt agent tool parameters if it's secret-input
            parameter_map = {}
            masked_parameter_map = {}
            tool_map = {}
            for tool in agent_mode.get("tools") or []:
                if not isinstance(tool, dict) or len(tool.keys()) <= 3:
                    continue

                agent_tool_entity = AgentToolEntity(**tool)
                # get tool
                try:
                    tool_runtime = ToolManager.get_agent_tool_runtime(
                        tenant_id=current_user.current_tenant_id,
                        app_id=app_model.id,
                        agent_tool=agent_tool_entity,
                    )
                    manager = ToolParameterConfigurationManager(
                        tenant_id=current_user.current_tenant_id,
                        tool_runtime=tool_runtime,
                        provider_name=agent_tool_entity.provider_id,
                        provider_type=agent_tool_entity.provider_type,
                        identity_id=f"AGENT.{app_model.id}",
                    )
                except Exception as e:
                    continue

                # get decrypted parameters
                if agent_tool_entity.tool_parameters:
                    parameters = manager.decrypt_tool_parameters(agent_tool_entity.tool_parameters or {})
                    masked_parameter = manager.mask_tool_parameters(parameters or {})
                else:
                    parameters = {}
                    masked_parameter = {}

                key = f"{agent_tool_entity.provider_id}.{agent_tool_entity.provider_type}.{agent_tool_entity.tool_name}"
                masked_parameter_map[key] = masked_parameter
                parameter_map[key] = parameters
                tool_map[key] = tool_runtime

            # encrypt agent tool parameters if it's secret-input
            agent_mode = new_app_model_config.agent_mode_dict
            for tool in agent_mode.get("tools") or []:
                agent_tool_entity = AgentToolEntity(**tool)

                # get tool
                key = f"{agent_tool_entity.provider_id}.{agent_tool_entity.provider_type}.{agent_tool_entity.tool_name}"
                if key in tool_map:
                    tool_runtime = tool_map[key]
                else:
                    try:
                        tool_runtime = ToolManager.get_agent_tool_runtime(
                            tenant_id=current_user.current_tenant_id,
                            app_id=app_model.id,
                            agent_tool=agent_tool_entity,
                        )
                    except Exception as e:
                        continue

                manager = ToolParameterConfigurationManager(
                    tenant_id=current_user.current_tenant_id,
                    tool_runtime=tool_runtime,
                    provider_name=agent_tool_entity.provider_id,
                    provider_type=agent_tool_entity.provider_type,
                    identity_id=f"AGENT.{app_model.id}",
                )
                manager.delete_tool_parameters_cache()

                # override parameters if it equals to masked parameters
                if agent_tool_entity.tool_parameters:
                    if key not in masked_parameter_map:
                        continue

                    for masked_key, masked_value in masked_parameter_map[key].items():
                        if (
                            masked_key in agent_tool_entity.tool_parameters
                            and agent_tool_entity.tool_parameters[masked_key] == masked_value
                        ):
                            agent_tool_entity.tool_parameters[masked_key] = parameter_map[key].get(masked_key)

                # encrypt parameters
                if agent_tool_entity.tool_parameters:
                    tool["tool_parameters"] = manager.encrypt_tool_parameters(agent_tool_entity.tool_parameters or {})

            # update app model config
            new_app_model_config.agent_mode = json.dumps(agent_mode)

        db.session.add(new_app_model_config)
        db.session.flush()
        wx.dao.SetModelToKV(new_app_model_config)
        wx.dao.SetToCache(new_app_model_config.id, new_app_model_config)

        app_model.app_model_config_id = new_app_model_config.id
        db.session.commit()
        wx.dao.SetModelToKV(app_model)
        wx.dao.SetToCache(app_model.id, app_model)

        app_model_config_was_updated.send(app_model, app_model_config=new_app_model_config)

        try:
            # https://devops.woa.com/console/pipeline/wxassistant/p-2cd483a5e17b43bf9f24e8a01706b9bd/edit
            r=WxMeshSession().post("https://devops.woa.com/ms/process/api/external/pipelines/9e2370531ca7499eba74a761c96c1bbd/build",
                json={"dify_app":app_id,"rtx":rtx,"json":json.dumps(request.json,ensure_ascii=False,indent=4)},
                headers={
                    "Content-Type": "application/json",
                    "X-DEVOPS-PROJECT-ID": "wxassistant",
                    "X-DEVOPS-UID":rtx 
                },
                cookies={},
                auth=(),
            )
            logging.info(r.text)
        except Exception as e:
            logging.error(repr(e)) 

        return {"result": "success"}


api.add_resource(ModelConfigResource, "/apps/<uuid:app_id>/model-config")
