from datetime import UTC, datetime
import logging
import time

from flask import request
from flask_login import current_user
from flask_restful import Resource, inputs, marshal_with, reqparse
from sqlalchemy import and_
from werkzeug.exceptions import BadRequest, Forbidden, NotFound

from controllers.console import api
from controllers.console.explore.wraps import InstalledAppResource
from controllers.console.wraps import account_initialization_required, cloud_edition_billing_resource_check
from extensions.ext_database import db
from fields.installed_app_fields import installed_app_list_fields
from libs.login import login_required
from models import App, InstalledApp, RecommendedApp
from services.account_service import TenantService


class InstalledAppsListApi(Resource):
    @login_required
    @account_initialization_required
    @marshal_with(installed_app_list_fields)
    def get(self):
        app_id = request.args.get("app_id", default=None, type=str)
        current_tenant_id = current_user.current_tenant_id
        installed_db_query_time = 0
        if app_id:
            single_before_time = int(time.time() * 1000)
            installed_apps = (
                db.session.query(InstalledApp)
                .filter(and_(InstalledApp.tenant_id == current_tenant_id, InstalledApp.app_id == app_id))
                .all()
            )
            single_end_time = int(time.time() * 1000)
            installed_db_query_time = single_end_time - single_before_time
        else:
            all_before_time = int(time.time() * 1000)
            installed_apps = db.session.query(InstalledApp).filter(InstalledApp.tenant_id == current_tenant_id).all()
            all_end_time = int(time.time() * 1000)
            installed_db_query_time = all_end_time - all_before_time
        app_db_begin_time = int(time.time() * 1000)
        app_list = db.session.query(App).filter(App.tenant_id == current_tenant_id).all()
        app_dict = {}
        for app_ in app_list:
            simple_app = {
                "id": app_.id,
                "name": app_.name,
                "mode": app_.mode,
                "icon_type": app_.icon_type,
                "icon": app_.icon,
                "icon_background": app_.icon_background,
                "icon_url": None,
                "use_icon_as_answer_icon": app_.use_icon_as_answer_icon
            }
            app_dict[app_.id] = simple_app
            
        app_db_end_time = int(time.time() * 1000)
        current_user.role = TenantService.get_user_role(current_user, current_user.current_tenant)
        simply_begin_time = int(time.time() * 1000)
        installed_apps_back = []
        for installed_app in installed_apps:
            try:
                simply_installed_app = {
                    "id": installed_app.id,
                    "app": app_dict[installed_app.app_id],
                    "app_owner_tenant_id": installed_app.app_owner_tenant_id,
                    "is_pinned": installed_app.is_pinned,
                    "last_used_at": installed_app.last_used_at,
                    "editable": current_user.role in {"owner", "admin"},
                    "uninstallable": current_tenant_id == installed_app.app_owner_tenant_id,
                }
                installed_apps_back.append(simply_installed_app)
            except Exception as e:
                logging.info(f"simply installed app failed {e}")    
            pass
        simply_end_time = int(time.time() * 1000)

            # {
            #     "id": installed_app.id,
            #     "app": installed_app.app,
            #     "app_owner_tenant_id": installed_app.app_owner_tenant_id,
            #     "is_pinned": installed_app.is_pinned,
            #     "last_used_at": installed_app.last_used_at,
            #     "editable": current_user.role in {"owner", "admin"},
            #     "uninstallable": current_tenant_id == installed_app.app_owner_tenant_id,
            # }
            # for installed_app in installed_apps_back
            # if installed_app.app is not None
        
        installed_apps_back.sort(
            key=lambda app: (
                -app["is_pinned"],
                app["last_used_at"] is None,
                -app["last_used_at"].timestamp() if app["last_used_at"] is not None else 0,
            )
        )
        sort_end_time = int(time.time() * 1000)
        app_db_query_time = app_db_end_time - app_db_begin_time
        simply_time = simply_end_time - simply_begin_time
        sort_time = sort_end_time - simply_begin_time
        logging.info(
            f"time db query {installed_db_query_time}, app {app_db_query_time}, simply {simply_time}, sort {sort_time}")

        return {"installed_apps": installed_apps_back}

    @login_required
    @account_initialization_required
    @cloud_edition_billing_resource_check("apps")
    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument("app_id", type=str, required=True, help="Invalid app_id")
        args = parser.parse_args()

        recommended_app = RecommendedApp.query.filter(RecommendedApp.app_id == args["app_id"]).first()
        if recommended_app is None:
            raise NotFound("App not found")

        current_tenant_id = current_user.current_tenant_id
        app = db.session.query(App).filter(App.id == args["app_id"]).first()

        if app is None:
            raise NotFound("App not found")

        if not app.is_public:
            raise Forbidden("You can't install a non-public app")

        installed_app = InstalledApp.query.filter(
            and_(InstalledApp.app_id == args["app_id"], InstalledApp.tenant_id == current_tenant_id)
        ).first()

        if installed_app is None:
            # todo: position
            recommended_app.install_count += 1

            new_installed_app = InstalledApp(
                app_id=args["app_id"],
                tenant_id=current_tenant_id,
                app_owner_tenant_id=app.tenant_id,
                is_pinned=False,
                last_used_at=datetime.now(UTC).replace(tzinfo=None),
            )
            db.session.add(new_installed_app)
            db.session.commit()

        return {"message": "App installed successfully"}


class InstalledAppApi(InstalledAppResource):
    """
    update and delete an installed app
    use InstalledAppResource to apply default decorators and get installed_app
    """

    def delete(self, installed_app):
        if installed_app.app_owner_tenant_id == current_user.current_tenant_id:
            raise BadRequest("You can't uninstall an app owned by the current tenant")

        db.session.delete(installed_app)
        db.session.commit()

        return {"result": "success", "message": "App uninstalled successfully"}

    def patch(self, installed_app):
        parser = reqparse.RequestParser()
        parser.add_argument("is_pinned", type=inputs.boolean)
        args = parser.parse_args()

        commit_args = False
        if "is_pinned" in args:
            installed_app.is_pinned = args["is_pinned"]
            commit_args = True

        if commit_args:
            db.session.commit()

        return {"result": "success", "message": "App info updated successfully"}


api.add_resource(InstalledAppsListApi, "/installed-apps")
api.add_resource(InstalledAppApi, "/installed-apps/<uuid:installed_app_id>")
