import logging
import time
import re
import os

from flask_restful import Resource, reqparse
from flask import request
from libs.login import login_required
from .wraps import setup_required
from wx.tof_tool import get_identity
from wx.wecube_reporter import async_report


def get_current_rtx():
    if 'X-Tai-Identity' in request.headers or 'X-Rio-Seq' in request.headers:
        try:
            header = {
                'x-tai-identity': request.headers.get('X-Tai-Identity'),
                'timestamp': request.headers.get('Timestamp'),
                'signature': request.headers.get('Signature'),
                'x-rio-seq': request.headers.get('X-Rio-Seq'),
            }
            identity = get_identity(header)
            rtx = identity['staffname']  # str
            logging.info(f"process_gateway_headers {rtx} {repr(identity)}")
            return rtx
        except Exception as e:
            logging.error(e)
            return ""


def get_regex_href(href: str):
    pattern = re.compile(
        r'^(?P<page>.*?)/(?P<app_id>[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})/(?P<menu>.*?)$'
    )
    # 执行匹配操作
    match = pattern.search(href)
    page = href
    app_id = ""
    menu = ""
    if match:
        page = match.group('page')
        app_id = match.group('app_id')
        menu = match.group('menu')
    return page, app_id, menu


class ReportClickHouseApi(Resource):
    @setup_required
    @login_required
    def post(self):
        start_timestamp = int(time.time() * 1000)
        req_timestamp = int(start_timestamp / 1000)
        parser = reqparse.RequestParser()
        parser.add_argument("duration", type=int, required=True)
        parser.add_argument("href", type=str, required=True)
        parser.add_argument("type", type=str, required=True)
        args = parser.parse_args()
        duration = args["duration"]
        href = args["href"]
        type_ = args["type"]
        page, app_id, menu = get_regex_href(href)
        current_rtx = get_current_rtx()
        logging.info(f"ReportClickHouseApi args {args}")
        report_data = {
            "biz_id": 15022,
            "duration": duration,
            "href": href,
            "type": type_,
            "rtx": current_rtx,
            "request_timestamp": req_timestamp,
            "page": page,
            "app_id": app_id,
            "menu": menu,
            'report_ip':os.getenv("POD_IP",''),
            'report_module':os.getenv("HOSTNAME",''),
        }
        async_report(report_data)
        end_timestamp = int(time.time() * 1000)
        report_duration = (end_timestamp - start_timestamp)
        logging.info(f"Cube {report_data}, start: {start_timestamp}, end: {end_timestamp}, duration {report_duration}")
        return {
            "result": "success",
            "rtx": current_rtx,
            "duration": report_duration
        }
