from flask import request, make_response, current_app
from flask_restful import Resource, reqparse
from configs import dify_config
from controllers.mesh import api
import json
import requests
import logging
from controllers.mesh import mmfinderdrallinonehttpsvr_pb2
from werkzeug.http import parse_cookie
from flask import g
import os

native_forward = True


class WxMeshCall(Resource):
    cmdid2path = {
        1: ("post", "/v1/workflows/run"),
        2: ("post", "/v1/chat-messages"),
        3: ("get", "/v1/conversations"),
        4: ("delete", "/v1/conversations/{}"),
        5: ("get", "/v1/info"),
        6: ("post", "/v1/workflows/run"),  # svrkit快速回包
    }

    def post(self):
        return self.dispatch()

    def make_request(self, client, method, endpoint, **kwargs):
        method_func = getattr(client, method.lower())
        logging.error(
            f"[liweizheng] make_request method: {method} endpoint: {endpoint} kwargs: {kwargs}"
        )
        return method_func(endpoint, **kwargs)

    def forward(
        self,
        cmdid: int,
        data: bytes,
        headers: dict[str, str],
        cookies: dict[str, str],
        query_params: dict[str, str],
        path_params: list[str],
    ) -> requests.Response:
        inner_response = None
        if cmdid in self.cmdid2path:
            if native_forward:
                # print(current_app)
                client = current_app.test_client()
                logging.error(
                    f"[liweizheng] native client call cmdid: {cmdid} data: {data.hex()}"
                )
                # 高版本不支持直接传递cookies参数

                # client.set_cookie("localhost", "user_id", "123")
                for cookie_key, cookie_value in cookies.items():
                    client.set_cookie(cookie_key, cookie_value)
                # inner_response = client.post(
                #     self.cmdid2path[cmdid],
                #     data=data,
                #     headers=headers,
                # )
                inner_response = self.make_request(
                    client,
                    self.cmdid2path[cmdid][0],
                    self.cmdid2path[cmdid][1].format(*path_params),
                    data=data,
                    headers=headers,
                    query_string=query_params,
                )
                logging.error(
                    f"[liweizheng] native client response: {inner_response.text}"
                )
            else:
                # 转发修改后的数据到第二个接口
                inner_response = requests.post(
                    "http://localhost:7777/" + self.cmdid2path[cmdid],
                    data=data,
                    headers=headers,
                    cookies=cookies,
                )
                logging.error(
                    f"[liweizheng] mesh inner_response: {inner_response.text}"
                )
        else:
            logging.error(f"[liweizheng] mesh cmdid not found: {cmdid}")

        return inner_response

    def extract_mesh_request(self):
        # 解析请求参数
        cmdid = int(request.args.get("cmdid"))
        uin = request.args.get("uin")
        meshid = request.args.get("meshid")
        logging.error(f"[liweizheng] mesh cmdid: {cmdid} uin: {uin} meshid: {meshid}")

        # 获取请求的原始二进制数据（适用于 application/octet-stream）
        raw_data = request.data  # bytes 类型
        # raw_data = bytes.fromhex(
        #     "0a420a0d417574686f72697a6174696f6e12314265617265722061707069642d31313633396536372d336364352d343261372d623830652d6139663662643664366531640a200a0c436f6e74656e742d5479706512106170706c69636174696f6e2f6a736f6e12300a0a64656275675f696e666f12227468697320697320646562756720696e666f2066726f6d206c697765697a68656e67121c0a0867726170685f69641210746869732069732067726170685f6964121f0a0c6d73675f74726163655f6964120f746869732069732074726163656964120f0a0375696e12083534393332383337122e0a1177785f726f626f745f757365726e616d651219746869732069732077785f726f626f745f757365726e616d651a527b22696e70757473223a7b226b6579223a22746869735f69735f615f6b6579222c227175657279223a22227d2c22726573706f6e73655f6d6f6465223a22626c6f636b696e67222c2275736572223a22227d"
        # )  # mock
        logging.error(f"[liweizheng] mesh raw_data hex: {raw_data.hex()}")
        logging.error(f"[liweizheng] mesh raw_data length: {len(raw_data)}")

        req = mmfinderdrallinonehttpsvr_pb2.CommonReq()
        req.ParseFromString(raw_data)  # 解析二进制数据

        logging.error(f"[liweizheng] mesh req hex: {req.data.hex()}")
        logging.error(f"[liweizheng] mesh headers: {req.headers}")

        headers = {}
        for header in req.headers:
            logging.error(
                f"[liweizheng] mesh headers info Key: {header.key}, Value: {header.value}"
            )
            headers[header.key] = header.value

        cookies = {}
        for cookie in req.cookies:
            logging.error(
                f"[liweizheng] mesh cookies info Key: {cookie.key}, Value: {cookie.value}"
            )
            cookies[cookie.key] = cookie.value

        query_params = {}
        for param in req.query_params:
            logging.error(
                f"[liweizheng] mesh query_params info Key: {param.key}, Value: {param.value}"
            )
            query_params[param.key] = param.value

        path_params = []
        for param in req.path_params:
            logging.error(f"[liweizheng] mesh path_params info Key: {param.key}")
            path_params.append(param.key)

        return cmdid, uin, meshid, req.data, headers, cookies, query_params, path_params

    def pack_mesh_response(self, forward_response):
        wxmesh_result = 0
        if forward_response:
            resp = mmfinderdrallinonehttpsvr_pb2.CommonResp()
            resp.status_code = forward_response.status_code
            # 设置响应头
            for k, v in forward_response.headers.items():
                resp.headers.append(
                    mmfinderdrallinonehttpsvr_pb2.Header(key=k, value=v)
                )

            # 设置响应体
            if native_forward:
                resp.data = forward_response.data
                resp.status = forward_response.status
                resp.ip = os.getenv("POD_IP", "")
                # 设置cookie
                set_cookie_headers = forward_response.headers.getlist("Set-Cookie")
                for cookie_header in set_cookie_headers:
                    cookie_dict = parse_cookie(cookie_header)
                    for cookie_name, cookie_value in cookie_dict.items():
                        print(f"{cookie_name}: {cookie_value}")
                        resp.headers.append(
                            mmfinderdrallinonehttpsvr_pb2.Header(
                                key=cookie_name, value=cookie_value
                            )
                        )
            else:
                resp.data = forward_response.content  # bytes
                resp.status = forward_response.reason
                # 设置cookie
                for cookie in forward_response.cookies:
                    resp.headers.append(
                        mmfinderdrallinonehttpsvr_pb2.Header(
                            key=cookie.name, value=cookie.value
                        )
                    )

            response_data = resp.SerializeToString()
            logging.error(f"[liweizheng] mesh packed resp: {repr(resp)}")
            logging.error(f"[liweizheng] mesh response length: {len(response_data)}")
            logging.error(f"[liweizheng] mesh response hex: {response_data.hex()}")

            # 创建响应对象
            response = make_response(response_data, forward_response.status_code)
        else:
            wxmesh_result = -19  # cmdid不存在，接口没实现
            response = make_response(b"", 404)

        # 设置自定义响应头
        # response.headers["Content-Type"] = "application/octet-stream"
        # response.headers["Content-Length"] = content_length

        response.headers["X-WxMesh-Result"] = wxmesh_result

        return response

    def add_report_data(self, cookies: dict[str, str]):
        uin = cookies.get("uin", "0")

        graph_id = cookies.get("graph_id", "")
        g.extend_info["traceid"] = f"{graph_id},{uin}" if uin and graph_id else ""
        g.extend_info["uin"] = uin if uin else "0"

    def add_mesh_info_to_cookies(
        self, cookies: dict[str, str], meshuin: str, meshid: str
    ):
        cookies["meshuin"] = meshuin
        cookies["meshid"] = meshid

    def dispatch(self):
        # 解析请求参数
        cmdid, uin, meshid, raw_body, headers, cookies, query_params, path_params = (
            self.extract_mesh_request()
        )
        # 增加日志打印额外参数
        self.add_report_data(cookies)
        # 增加mesh信息如meshid和meshuin到cookies，透传到下游
        self.add_mesh_info_to_cookies(cookies, uin, meshid)
        # 转发
        forward_response = self.forward(
            cmdid, raw_body, headers, cookies, query_params, path_params
        )

        # 打包响应
        return self.pack_mesh_response(forward_response)


api.add_resource(WxMeshCall, "/WxMeshCall")
