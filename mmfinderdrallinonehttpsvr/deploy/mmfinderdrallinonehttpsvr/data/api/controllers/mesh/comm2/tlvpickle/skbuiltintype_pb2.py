# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: comm2/tlvpickle/skbuiltintype.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import descriptor_pb2 as google_dot_protobuf_dot_descriptor__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='comm2/tlvpickle/skbuiltintype.proto',
  package='tlvpickle',
  syntax='proto2',
  serialized_options=_b('\n\025com.tencent.tlvpickleB\ttlvpickle'),
  serialized_pb=_b('\n#comm2/tlvpickle/skbuiltintype.proto\x12\ttlvpickle\x1a google/protobuf/descriptor.proto\"!\n\x11SKBuiltinInt32_PB\x12\x0c\n\x04iVal\x18\x01 \x02(\r\"#\n\x12SKBuiltinUint32_PB\x12\r\n\x05uiVal\x18\x01 \x02(\r\" \n\x10SKBuiltinChar_PB\x12\x0c\n\x04iVal\x18\x01 \x02(\x05\"\"\n\x11SKBuiltinUchar_PB\x12\r\n\x05uiVal\x18\x01 \x02(\r\" \n\x10SKBuiltinInt8_PB\x12\x0c\n\x04iVal\x18\x01 \x02(\x05\"\"\n\x11SKBuiltinUint8_PB\x12\r\n\x05uiVal\x18\x01 \x02(\r\"!\n\x11SKBuiltinInt16_PB\x12\x0c\n\x04iVal\x18\x01 \x02(\x05\"#\n\x12SKBuiltinUint16_PB\x12\r\n\x05uiVal\x18\x01 \x02(\r\"\"\n\x11SKBuiltinInt64_PB\x12\r\n\x05llVal\x18\x01 \x02(\x03\"$\n\x12SKBuiltinUint64_PB\x12\x0e\n\x06ullVal\x18\x01 \x02(\x04\"#\n\x13SKBuiltinFloat32_PB\x12\x0c\n\x04\x66Val\x18\x01 \x02(\x02\"$\n\x14SKBuiltinDouble64_PB\x12\x0c\n\x04\x64Val\x18\x01 \x02(\x01\"2\n\x12SKBuiltinBuffer_PB\x12\x0c\n\x04iLen\x18\x01 \x02(\r\x12\x0e\n\x06\x42uffer\x18\x02 \x01(\x0c\"$\n\x12SKBuiltinString_PB\x12\x0e\n\x06String\x18\x01 \x01(\t\"#\n\x11SKBuiltinBytes_PB\x12\x0e\n\x06\x42uffer\x18\x01 \x01(\x0c\"\x13\n\x11SKBuiltinEmpty_PB\"\x15\n\x13SKBuiltinNoexsit_PB\"8\n\x14SKBuiltinEchoInfo_PB\x12\x0f\n\x07\x45\x63hoLen\x18\x01 \x02(\x05\x12\x0f\n\x07\x45\x63hoStr\x18\x02 \x02(\x0c\"P\n\x0cSKPBMetaInfo\x12\x11\n\tfile_name\x18\x01 \x01(\t\x12-\n\x06\x62uffer\x18\x02 \x01(\x0b\x32\x1d.tlvpickle.SKBuiltinBuffer_PB\"9\n\x10SKPBMetaInfoResp\x12%\n\x04info\x18\x01 \x03(\x0b\x32\x17.tlvpickle.SKPBMetaInfo\"R\n\x0cSKProfileReq\x12\r\n\x05index\x18\x01 \x01(\x05\x12\x0c\n\x04oper\x18\x02 \x01(\x05\x12\x14\n\x0cprofile_type\x18\x03 \x01(\x05\x12\x0f\n\x07timeout\x18\x04 \x01(\x05\"\x1f\n\rSKProfileResp\x12\x0e\n\x06result\x18\x01 \x01(\t\"\x18\n\x16SKBuiltinGenericMsg_PB\"\x9f\x01\n\x0cSKByPassInfo\x12\x0c\n\x04open\x18\x01 \x02(\x05\x12\x12\n\npercentage\x18\x02 \x02(\x05\x12\r\n\x05topic\x18\x03 \x02(\t\x12\x0b\n\x03key\x18\x04 \x01(\t\x12\x0f\n\x07\x63luster\x18\x05 \x02(\t\x12\x13\n\x0bskip_cmdids\x18\x06 \x03(\x05\x12\x0c\n\x04uins\x18\x07 \x03(\r\x12\x1d\n\x0cmax_pkg_size\x18\x08 \x01(\r:\x07\x34\x31\x39\x34\x33\x30\x34\"?\n\x12SKReport2WQueueReq\x12\r\n\x05topic\x18\x02 \x01(\t\x12\x0b\n\x03key\x18\x03 \x01(\x0c\x12\r\n\x05value\x18\x04 \x01(\x0c\"Y\n\x0bSKExtendRet\x12\x13\n\x0bReadTimeout\x18\x01 \x02(\x05\x12\x10\n\x08\x46\x61stFail\x18\x02 \x02(\x05\x12\x11\n\tTicketErr\x18\x03 \x02(\x05\x12\x10\n\x08OtherErr\x18\x04 \x02(\x05\"A\n\nSKFieldMap\x12\x0f\n\x07ReqText\x18\x01 \x01(\t\x12\x11\n\tReqTextWx\x18\x02 \x01(\t\x12\x0f\n\x07RetText\x18\x03 \x01(\t*D\n\x10MMClientCallType\x12\x17\n\x13kMMClientCallNormal\x10\x00\x12\x17\n\x13kMMClientCallStrict\x10\x01*F\n\x11SvrkitProcessType\x12\x18\n\x14kSvrkitProcessNormal\x10\x00\x12\x17\n\x13kSvrkitProcessAsync\x10\x01::\n\rEnumValueDesc\x12!.google.protobuf.EnumValueOptions\x18\xc0\x84= \x01(\t:0\n\x08\x45numDesc\x12\x1c.google.protobuf.EnumOptions\x18\xc0\x84= \x01(\t:-\n\x04\x44\x65sc\x12\x1d.google.protobuf.FieldOptions\x18\xca\x84= \x01(\t:,\n\x03Min\x12\x1d.google.protobuf.FieldOptions\x18\xcb\x84= \x01(\x04:,\n\x03Max\x12\x1d.google.protobuf.FieldOptions\x18\xcc\x84= \x01(\x04:2\n\tValueList\x12\x1d.google.protobuf.FieldOptions\x18\xcd\x84= \x01(\t:4\n\x0bSectionName\x12\x1d.google.protobuf.FieldOptions\x18\xce\x84= \x01(\t:0\n\x07KeyName\x12\x1d.google.protobuf.FieldOptions\x18\xcf\x84= \x01(\t:4\n\x0bWxUin64Type\x12\x1d.google.protobuf.FieldOptions\x18\xd1\x84= \x01(\t:5\n\nServerType\x12\x1f.google.protobuf.ServiceOptions\x18\xc0\x84= \x01(\t:0\n\x05Magic\x12\x1f.google.protobuf.ServiceOptions\x18\xc1\x84= \x01(\x05:1\n\x06Target\x12\x1f.google.protobuf.ServiceOptions\x18\xc2\x84= \x01(\x05:6\n\x0bRouteMethod\x12\x1f.google.protobuf.ServiceOptions\x18\xc3\x84= \x01(\t:5\n\nUseGateway\x12\x1f.google.protobuf.ServiceOptions\x18\xc4\x84= \x01(\x05:7\n\x0cTemplateType\x12\x1f.google.protobuf.ServiceOptions\x18\xc5\x84= \x01(\t:1\n\x06MQType\x12\x1f.google.protobuf.ServiceOptions\x18\xc6\x84= \x01(\x05:;\n\x10UseWxPayMultiSet\x12\x1f.google.protobuf.ServiceOptions\x18\xc7\x84= \x01(\x05:;\n\x10\x45nableAttachment\x12\x1f.google.protobuf.ServiceOptions\x18\xc8\x84= \x01(\x05:=\n\x12\x43lientAllowDepends\x12\x1f.google.protobuf.ServiceOptions\x18\xc9\x84= \x03(\t:6\n\x0bUseMultienv\x12\x1f.google.protobuf.ServiceOptions\x18\xca\x84= \x01(\x05:=\n\x12ServiceRouteMethod\x12\x1f.google.protobuf.ServiceOptions\x18\xcb\x84= \x01(\t:2\n\x07UinType\x12\x1f.google.protobuf.ServiceOptions\x18\xcc\x84= \x01(\t:8\n\rServerUinType\x12\x1f.google.protobuf.ServiceOptions\x18\xcd\x84= \x01(\t:8\n\rClientUinType\x12\x1f.google.protobuf.ServiceOptions\x18\xce\x84= \x01(\t:/\n\x05\x43mdID\x12\x1e.google.protobuf.MethodOptions\x18\xc0\x84= \x01(\x05:3\n\tOptString\x12\x1e.google.protobuf.MethodOptions\x18\xc1\x84= \x01(\t:/\n\x05Usage\x12\x1e.google.protobuf.MethodOptions\x18\xc2\x84= \x01(\t:4\n\nNeedExtEnd\x12\x1e.google.protobuf.MethodOptions\x18\xc3\x84= \x01(\x05:K\n\tExtendRet\x12\x1e.google.protobuf.MethodOptions\x18\xc4\x84= \x01(\x0b\x32\x16.tlvpickle.SKExtendRet:/\n\x05\x42rief\x12\x1e.google.protobuf.MethodOptions\x18\xc5\x84= \x01(\t:-\n\x03Url\x12\x1e.google.protobuf.MethodOptions\x18\xc6\x84= \x01(\t:I\n\x08\x46ieldMap\x12\x1e.google.protobuf.MethodOptions\x18\xc7\x84= \x01(\x0b\x32\x15.tlvpickle.SKFieldMap:2\n\x08RespType\x12\x1e.google.protobuf.MethodOptions\x18\xc8\x84= \x01(\x05:U\n\x0e\x43lientCallType\x12\x1e.google.protobuf.MethodOptions\x18\xc9\x84= \x01(\x0e\x32\x1b.tlvpickle.MMClientCallType:7\n\rMQHandlerType\x12\x1e.google.protobuf.MethodOptions\x18\xca\x84= \x01(\x05:9\n\x0f\x42usinessErrCode\x12\x1e.google.protobuf.MethodOptions\x18\xcb\x84= \x01(\t:<\n\x12\x45nableReqValidator\x12\x1e.google.protobuf.MethodOptions\x18\xcc\x84= \x01(\x08:S\n\x0bProcessType\x12\x1e.google.protobuf.MethodOptions\x18\xcd\x84= \x01(\x0e\x32\x1c.tlvpickle.SvrkitProcessType:7\n\rIsBatchMethod\x12\x1e.google.protobuf.MethodOptions\x18\xce\x84= \x01(\x05:8\n\x0eRpcRouteMethod\x12\x1e.google.protobuf.MethodOptions\x18\xcf\x84= \x01(\tB\"\n\x15\x63om.tencent.tlvpickleB\ttlvpickle')
  ,
  dependencies=[google_dot_protobuf_dot_descriptor__pb2.DESCRIPTOR,])

_MMCLIENTCALLTYPE = _descriptor.EnumDescriptor(
  name='MMClientCallType',
  full_name='tlvpickle.MMClientCallType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='kMMClientCallNormal', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='kMMClientCallStrict', index=1, number=1,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1415,
  serialized_end=1483,
)
_sym_db.RegisterEnumDescriptor(_MMCLIENTCALLTYPE)

MMClientCallType = enum_type_wrapper.EnumTypeWrapper(_MMCLIENTCALLTYPE)
_SVRKITPROCESSTYPE = _descriptor.EnumDescriptor(
  name='SvrkitProcessType',
  full_name='tlvpickle.SvrkitProcessType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='kSvrkitProcessNormal', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='kSvrkitProcessAsync', index=1, number=1,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1485,
  serialized_end=1555,
)
_sym_db.RegisterEnumDescriptor(_SVRKITPROCESSTYPE)

SvrkitProcessType = enum_type_wrapper.EnumTypeWrapper(_SVRKITPROCESSTYPE)
kMMClientCallNormal = 0
kMMClientCallStrict = 1
kSvrkitProcessNormal = 0
kSvrkitProcessAsync = 1

ENUMVALUEDESC_FIELD_NUMBER = 1000000
EnumValueDesc = _descriptor.FieldDescriptor(
  name='EnumValueDesc', full_name='tlvpickle.EnumValueDesc', index=0,
  number=1000000, type=9, cpp_type=9, label=1,
  has_default_value=False, default_value=_b("").decode('utf-8'),
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
ENUMDESC_FIELD_NUMBER = 1000000
EnumDesc = _descriptor.FieldDescriptor(
  name='EnumDesc', full_name='tlvpickle.EnumDesc', index=1,
  number=1000000, type=9, cpp_type=9, label=1,
  has_default_value=False, default_value=_b("").decode('utf-8'),
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
DESC_FIELD_NUMBER = 1000010
Desc = _descriptor.FieldDescriptor(
  name='Desc', full_name='tlvpickle.Desc', index=2,
  number=1000010, type=9, cpp_type=9, label=1,
  has_default_value=False, default_value=_b("").decode('utf-8'),
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
MIN_FIELD_NUMBER = 1000011
Min = _descriptor.FieldDescriptor(
  name='Min', full_name='tlvpickle.Min', index=3,
  number=1000011, type=4, cpp_type=4, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
MAX_FIELD_NUMBER = 1000012
Max = _descriptor.FieldDescriptor(
  name='Max', full_name='tlvpickle.Max', index=4,
  number=1000012, type=4, cpp_type=4, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
VALUELIST_FIELD_NUMBER = 1000013
ValueList = _descriptor.FieldDescriptor(
  name='ValueList', full_name='tlvpickle.ValueList', index=5,
  number=1000013, type=9, cpp_type=9, label=1,
  has_default_value=False, default_value=_b("").decode('utf-8'),
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
SECTIONNAME_FIELD_NUMBER = 1000014
SectionName = _descriptor.FieldDescriptor(
  name='SectionName', full_name='tlvpickle.SectionName', index=6,
  number=1000014, type=9, cpp_type=9, label=1,
  has_default_value=False, default_value=_b("").decode('utf-8'),
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
KEYNAME_FIELD_NUMBER = 1000015
KeyName = _descriptor.FieldDescriptor(
  name='KeyName', full_name='tlvpickle.KeyName', index=7,
  number=1000015, type=9, cpp_type=9, label=1,
  has_default_value=False, default_value=_b("").decode('utf-8'),
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
WXUIN64TYPE_FIELD_NUMBER = 1000017
WxUin64Type = _descriptor.FieldDescriptor(
  name='WxUin64Type', full_name='tlvpickle.WxUin64Type', index=8,
  number=1000017, type=9, cpp_type=9, label=1,
  has_default_value=False, default_value=_b("").decode('utf-8'),
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
SERVERTYPE_FIELD_NUMBER = 1000000
ServerType = _descriptor.FieldDescriptor(
  name='ServerType', full_name='tlvpickle.ServerType', index=9,
  number=1000000, type=9, cpp_type=9, label=1,
  has_default_value=False, default_value=_b("").decode('utf-8'),
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
MAGIC_FIELD_NUMBER = 1000001
Magic = _descriptor.FieldDescriptor(
  name='Magic', full_name='tlvpickle.Magic', index=10,
  number=1000001, type=5, cpp_type=1, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
TARGET_FIELD_NUMBER = 1000002
Target = _descriptor.FieldDescriptor(
  name='Target', full_name='tlvpickle.Target', index=11,
  number=1000002, type=5, cpp_type=1, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
ROUTEMETHOD_FIELD_NUMBER = 1000003
RouteMethod = _descriptor.FieldDescriptor(
  name='RouteMethod', full_name='tlvpickle.RouteMethod', index=12,
  number=1000003, type=9, cpp_type=9, label=1,
  has_default_value=False, default_value=_b("").decode('utf-8'),
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
USEGATEWAY_FIELD_NUMBER = 1000004
UseGateway = _descriptor.FieldDescriptor(
  name='UseGateway', full_name='tlvpickle.UseGateway', index=13,
  number=1000004, type=5, cpp_type=1, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
TEMPLATETYPE_FIELD_NUMBER = 1000005
TemplateType = _descriptor.FieldDescriptor(
  name='TemplateType', full_name='tlvpickle.TemplateType', index=14,
  number=1000005, type=9, cpp_type=9, label=1,
  has_default_value=False, default_value=_b("").decode('utf-8'),
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
MQTYPE_FIELD_NUMBER = 1000006
MQType = _descriptor.FieldDescriptor(
  name='MQType', full_name='tlvpickle.MQType', index=15,
  number=1000006, type=5, cpp_type=1, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
USEWXPAYMULTISET_FIELD_NUMBER = 1000007
UseWxPayMultiSet = _descriptor.FieldDescriptor(
  name='UseWxPayMultiSet', full_name='tlvpickle.UseWxPayMultiSet', index=16,
  number=1000007, type=5, cpp_type=1, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
ENABLEATTACHMENT_FIELD_NUMBER = 1000008
EnableAttachment = _descriptor.FieldDescriptor(
  name='EnableAttachment', full_name='tlvpickle.EnableAttachment', index=17,
  number=1000008, type=5, cpp_type=1, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
CLIENTALLOWDEPENDS_FIELD_NUMBER = 1000009
ClientAllowDepends = _descriptor.FieldDescriptor(
  name='ClientAllowDepends', full_name='tlvpickle.ClientAllowDepends', index=18,
  number=1000009, type=9, cpp_type=9, label=3,
  has_default_value=False, default_value=[],
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
USEMULTIENV_FIELD_NUMBER = 1000010
UseMultienv = _descriptor.FieldDescriptor(
  name='UseMultienv', full_name='tlvpickle.UseMultienv', index=19,
  number=1000010, type=5, cpp_type=1, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
SERVICEROUTEMETHOD_FIELD_NUMBER = 1000011
ServiceRouteMethod = _descriptor.FieldDescriptor(
  name='ServiceRouteMethod', full_name='tlvpickle.ServiceRouteMethod', index=20,
  number=1000011, type=9, cpp_type=9, label=1,
  has_default_value=False, default_value=_b("").decode('utf-8'),
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
UINTYPE_FIELD_NUMBER = 1000012
UinType = _descriptor.FieldDescriptor(
  name='UinType', full_name='tlvpickle.UinType', index=21,
  number=1000012, type=9, cpp_type=9, label=1,
  has_default_value=False, default_value=_b("").decode('utf-8'),
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
SERVERUINTYPE_FIELD_NUMBER = 1000013
ServerUinType = _descriptor.FieldDescriptor(
  name='ServerUinType', full_name='tlvpickle.ServerUinType', index=22,
  number=1000013, type=9, cpp_type=9, label=1,
  has_default_value=False, default_value=_b("").decode('utf-8'),
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
CLIENTUINTYPE_FIELD_NUMBER = 1000014
ClientUinType = _descriptor.FieldDescriptor(
  name='ClientUinType', full_name='tlvpickle.ClientUinType', index=23,
  number=1000014, type=9, cpp_type=9, label=1,
  has_default_value=False, default_value=_b("").decode('utf-8'),
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
CMDID_FIELD_NUMBER = 1000000
CmdID = _descriptor.FieldDescriptor(
  name='CmdID', full_name='tlvpickle.CmdID', index=24,
  number=1000000, type=5, cpp_type=1, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
OPTSTRING_FIELD_NUMBER = 1000001
OptString = _descriptor.FieldDescriptor(
  name='OptString', full_name='tlvpickle.OptString', index=25,
  number=1000001, type=9, cpp_type=9, label=1,
  has_default_value=False, default_value=_b("").decode('utf-8'),
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
USAGE_FIELD_NUMBER = 1000002
Usage = _descriptor.FieldDescriptor(
  name='Usage', full_name='tlvpickle.Usage', index=26,
  number=1000002, type=9, cpp_type=9, label=1,
  has_default_value=False, default_value=_b("").decode('utf-8'),
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
NEEDEXTEND_FIELD_NUMBER = 1000003
NeedExtEnd = _descriptor.FieldDescriptor(
  name='NeedExtEnd', full_name='tlvpickle.NeedExtEnd', index=27,
  number=1000003, type=5, cpp_type=1, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
EXTENDRET_FIELD_NUMBER = 1000004
ExtendRet = _descriptor.FieldDescriptor(
  name='ExtendRet', full_name='tlvpickle.ExtendRet', index=28,
  number=1000004, type=11, cpp_type=10, label=1,
  has_default_value=False, default_value=None,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
BRIEF_FIELD_NUMBER = 1000005
Brief = _descriptor.FieldDescriptor(
  name='Brief', full_name='tlvpickle.Brief', index=29,
  number=1000005, type=9, cpp_type=9, label=1,
  has_default_value=False, default_value=_b("").decode('utf-8'),
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
URL_FIELD_NUMBER = 1000006
Url = _descriptor.FieldDescriptor(
  name='Url', full_name='tlvpickle.Url', index=30,
  number=1000006, type=9, cpp_type=9, label=1,
  has_default_value=False, default_value=_b("").decode('utf-8'),
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
FIELDMAP_FIELD_NUMBER = 1000007
FieldMap = _descriptor.FieldDescriptor(
  name='FieldMap', full_name='tlvpickle.FieldMap', index=31,
  number=1000007, type=11, cpp_type=10, label=1,
  has_default_value=False, default_value=None,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
RESPTYPE_FIELD_NUMBER = 1000008
RespType = _descriptor.FieldDescriptor(
  name='RespType', full_name='tlvpickle.RespType', index=32,
  number=1000008, type=5, cpp_type=1, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
CLIENTCALLTYPE_FIELD_NUMBER = 1000009
ClientCallType = _descriptor.FieldDescriptor(
  name='ClientCallType', full_name='tlvpickle.ClientCallType', index=33,
  number=1000009, type=14, cpp_type=8, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
MQHANDLERTYPE_FIELD_NUMBER = 1000010
MQHandlerType = _descriptor.FieldDescriptor(
  name='MQHandlerType', full_name='tlvpickle.MQHandlerType', index=34,
  number=1000010, type=5, cpp_type=1, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
BUSINESSERRCODE_FIELD_NUMBER = 1000011
BusinessErrCode = _descriptor.FieldDescriptor(
  name='BusinessErrCode', full_name='tlvpickle.BusinessErrCode', index=35,
  number=1000011, type=9, cpp_type=9, label=1,
  has_default_value=False, default_value=_b("").decode('utf-8'),
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
ENABLEREQVALIDATOR_FIELD_NUMBER = 1000012
EnableReqValidator = _descriptor.FieldDescriptor(
  name='EnableReqValidator', full_name='tlvpickle.EnableReqValidator', index=36,
  number=1000012, type=8, cpp_type=7, label=1,
  has_default_value=False, default_value=False,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
PROCESSTYPE_FIELD_NUMBER = 1000013
ProcessType = _descriptor.FieldDescriptor(
  name='ProcessType', full_name='tlvpickle.ProcessType', index=37,
  number=1000013, type=14, cpp_type=8, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
ISBATCHMETHOD_FIELD_NUMBER = 1000014
IsBatchMethod = _descriptor.FieldDescriptor(
  name='IsBatchMethod', full_name='tlvpickle.IsBatchMethod', index=38,
  number=1000014, type=5, cpp_type=1, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)
RPCROUTEMETHOD_FIELD_NUMBER = 1000015
RpcRouteMethod = _descriptor.FieldDescriptor(
  name='RpcRouteMethod', full_name='tlvpickle.RpcRouteMethod', index=39,
  number=1000015, type=9, cpp_type=9, label=1,
  has_default_value=False, default_value=_b("").decode('utf-8'),
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  serialized_options=None, file=DESCRIPTOR)


_SKBUILTININT32_PB = _descriptor.Descriptor(
  name='SKBuiltinInt32_PB',
  full_name='tlvpickle.SKBuiltinInt32_PB',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='iVal', full_name='tlvpickle.SKBuiltinInt32_PB.iVal', index=0,
      number=1, type=13, cpp_type=3, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=84,
  serialized_end=117,
)


_SKBUILTINUINT32_PB = _descriptor.Descriptor(
  name='SKBuiltinUint32_PB',
  full_name='tlvpickle.SKBuiltinUint32_PB',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='uiVal', full_name='tlvpickle.SKBuiltinUint32_PB.uiVal', index=0,
      number=1, type=13, cpp_type=3, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=119,
  serialized_end=154,
)


_SKBUILTINCHAR_PB = _descriptor.Descriptor(
  name='SKBuiltinChar_PB',
  full_name='tlvpickle.SKBuiltinChar_PB',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='iVal', full_name='tlvpickle.SKBuiltinChar_PB.iVal', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=156,
  serialized_end=188,
)


_SKBUILTINUCHAR_PB = _descriptor.Descriptor(
  name='SKBuiltinUchar_PB',
  full_name='tlvpickle.SKBuiltinUchar_PB',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='uiVal', full_name='tlvpickle.SKBuiltinUchar_PB.uiVal', index=0,
      number=1, type=13, cpp_type=3, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=190,
  serialized_end=224,
)


_SKBUILTININT8_PB = _descriptor.Descriptor(
  name='SKBuiltinInt8_PB',
  full_name='tlvpickle.SKBuiltinInt8_PB',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='iVal', full_name='tlvpickle.SKBuiltinInt8_PB.iVal', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=226,
  serialized_end=258,
)


_SKBUILTINUINT8_PB = _descriptor.Descriptor(
  name='SKBuiltinUint8_PB',
  full_name='tlvpickle.SKBuiltinUint8_PB',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='uiVal', full_name='tlvpickle.SKBuiltinUint8_PB.uiVal', index=0,
      number=1, type=13, cpp_type=3, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=260,
  serialized_end=294,
)


_SKBUILTININT16_PB = _descriptor.Descriptor(
  name='SKBuiltinInt16_PB',
  full_name='tlvpickle.SKBuiltinInt16_PB',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='iVal', full_name='tlvpickle.SKBuiltinInt16_PB.iVal', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=296,
  serialized_end=329,
)


_SKBUILTINUINT16_PB = _descriptor.Descriptor(
  name='SKBuiltinUint16_PB',
  full_name='tlvpickle.SKBuiltinUint16_PB',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='uiVal', full_name='tlvpickle.SKBuiltinUint16_PB.uiVal', index=0,
      number=1, type=13, cpp_type=3, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=331,
  serialized_end=366,
)


_SKBUILTININT64_PB = _descriptor.Descriptor(
  name='SKBuiltinInt64_PB',
  full_name='tlvpickle.SKBuiltinInt64_PB',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='llVal', full_name='tlvpickle.SKBuiltinInt64_PB.llVal', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=368,
  serialized_end=402,
)


_SKBUILTINUINT64_PB = _descriptor.Descriptor(
  name='SKBuiltinUint64_PB',
  full_name='tlvpickle.SKBuiltinUint64_PB',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ullVal', full_name='tlvpickle.SKBuiltinUint64_PB.ullVal', index=0,
      number=1, type=4, cpp_type=4, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=404,
  serialized_end=440,
)


_SKBUILTINFLOAT32_PB = _descriptor.Descriptor(
  name='SKBuiltinFloat32_PB',
  full_name='tlvpickle.SKBuiltinFloat32_PB',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='fVal', full_name='tlvpickle.SKBuiltinFloat32_PB.fVal', index=0,
      number=1, type=2, cpp_type=6, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=442,
  serialized_end=477,
)


_SKBUILTINDOUBLE64_PB = _descriptor.Descriptor(
  name='SKBuiltinDouble64_PB',
  full_name='tlvpickle.SKBuiltinDouble64_PB',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='dVal', full_name='tlvpickle.SKBuiltinDouble64_PB.dVal', index=0,
      number=1, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=479,
  serialized_end=515,
)


_SKBUILTINBUFFER_PB = _descriptor.Descriptor(
  name='SKBuiltinBuffer_PB',
  full_name='tlvpickle.SKBuiltinBuffer_PB',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='iLen', full_name='tlvpickle.SKBuiltinBuffer_PB.iLen', index=0,
      number=1, type=13, cpp_type=3, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='Buffer', full_name='tlvpickle.SKBuiltinBuffer_PB.Buffer', index=1,
      number=2, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=517,
  serialized_end=567,
)


_SKBUILTINSTRING_PB = _descriptor.Descriptor(
  name='SKBuiltinString_PB',
  full_name='tlvpickle.SKBuiltinString_PB',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='String', full_name='tlvpickle.SKBuiltinString_PB.String', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=569,
  serialized_end=605,
)


_SKBUILTINBYTES_PB = _descriptor.Descriptor(
  name='SKBuiltinBytes_PB',
  full_name='tlvpickle.SKBuiltinBytes_PB',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='Buffer', full_name='tlvpickle.SKBuiltinBytes_PB.Buffer', index=0,
      number=1, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=607,
  serialized_end=642,
)


_SKBUILTINEMPTY_PB = _descriptor.Descriptor(
  name='SKBuiltinEmpty_PB',
  full_name='tlvpickle.SKBuiltinEmpty_PB',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=644,
  serialized_end=663,
)


_SKBUILTINNOEXSIT_PB = _descriptor.Descriptor(
  name='SKBuiltinNoexsit_PB',
  full_name='tlvpickle.SKBuiltinNoexsit_PB',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=665,
  serialized_end=686,
)


_SKBUILTINECHOINFO_PB = _descriptor.Descriptor(
  name='SKBuiltinEchoInfo_PB',
  full_name='tlvpickle.SKBuiltinEchoInfo_PB',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='EchoLen', full_name='tlvpickle.SKBuiltinEchoInfo_PB.EchoLen', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='EchoStr', full_name='tlvpickle.SKBuiltinEchoInfo_PB.EchoStr', index=1,
      number=2, type=12, cpp_type=9, label=2,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=688,
  serialized_end=744,
)


_SKPBMETAINFO = _descriptor.Descriptor(
  name='SKPBMetaInfo',
  full_name='tlvpickle.SKPBMetaInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='file_name', full_name='tlvpickle.SKPBMetaInfo.file_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='buffer', full_name='tlvpickle.SKPBMetaInfo.buffer', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=746,
  serialized_end=826,
)


_SKPBMETAINFORESP = _descriptor.Descriptor(
  name='SKPBMetaInfoResp',
  full_name='tlvpickle.SKPBMetaInfoResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='info', full_name='tlvpickle.SKPBMetaInfoResp.info', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=828,
  serialized_end=885,
)


_SKPROFILEREQ = _descriptor.Descriptor(
  name='SKProfileReq',
  full_name='tlvpickle.SKProfileReq',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='index', full_name='tlvpickle.SKProfileReq.index', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='oper', full_name='tlvpickle.SKProfileReq.oper', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='profile_type', full_name='tlvpickle.SKProfileReq.profile_type', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='timeout', full_name='tlvpickle.SKProfileReq.timeout', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=887,
  serialized_end=969,
)


_SKPROFILERESP = _descriptor.Descriptor(
  name='SKProfileResp',
  full_name='tlvpickle.SKProfileResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='tlvpickle.SKProfileResp.result', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=971,
  serialized_end=1002,
)


_SKBUILTINGENERICMSG_PB = _descriptor.Descriptor(
  name='SKBuiltinGenericMsg_PB',
  full_name='tlvpickle.SKBuiltinGenericMsg_PB',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1004,
  serialized_end=1028,
)


_SKBYPASSINFO = _descriptor.Descriptor(
  name='SKByPassInfo',
  full_name='tlvpickle.SKByPassInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='open', full_name='tlvpickle.SKByPassInfo.open', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='percentage', full_name='tlvpickle.SKByPassInfo.percentage', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='topic', full_name='tlvpickle.SKByPassInfo.topic', index=2,
      number=3, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='key', full_name='tlvpickle.SKByPassInfo.key', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cluster', full_name='tlvpickle.SKByPassInfo.cluster', index=4,
      number=5, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='skip_cmdids', full_name='tlvpickle.SKByPassInfo.skip_cmdids', index=5,
      number=6, type=5, cpp_type=1, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='uins', full_name='tlvpickle.SKByPassInfo.uins', index=6,
      number=7, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_pkg_size', full_name='tlvpickle.SKByPassInfo.max_pkg_size', index=7,
      number=8, type=13, cpp_type=3, label=1,
      has_default_value=True, default_value=4194304,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1031,
  serialized_end=1190,
)


_SKREPORT2WQUEUEREQ = _descriptor.Descriptor(
  name='SKReport2WQueueReq',
  full_name='tlvpickle.SKReport2WQueueReq',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='topic', full_name='tlvpickle.SKReport2WQueueReq.topic', index=0,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='key', full_name='tlvpickle.SKReport2WQueueReq.key', index=1,
      number=3, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='tlvpickle.SKReport2WQueueReq.value', index=2,
      number=4, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1192,
  serialized_end=1255,
)


_SKEXTENDRET = _descriptor.Descriptor(
  name='SKExtendRet',
  full_name='tlvpickle.SKExtendRet',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ReadTimeout', full_name='tlvpickle.SKExtendRet.ReadTimeout', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='FastFail', full_name='tlvpickle.SKExtendRet.FastFail', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='TicketErr', full_name='tlvpickle.SKExtendRet.TicketErr', index=2,
      number=3, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='OtherErr', full_name='tlvpickle.SKExtendRet.OtherErr', index=3,
      number=4, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1257,
  serialized_end=1346,
)


_SKFIELDMAP = _descriptor.Descriptor(
  name='SKFieldMap',
  full_name='tlvpickle.SKFieldMap',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ReqText', full_name='tlvpickle.SKFieldMap.ReqText', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ReqTextWx', full_name='tlvpickle.SKFieldMap.ReqTextWx', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='RetText', full_name='tlvpickle.SKFieldMap.RetText', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1348,
  serialized_end=1413,
)

_SKPBMETAINFO.fields_by_name['buffer'].message_type = _SKBUILTINBUFFER_PB
_SKPBMETAINFORESP.fields_by_name['info'].message_type = _SKPBMETAINFO
DESCRIPTOR.message_types_by_name['SKBuiltinInt32_PB'] = _SKBUILTININT32_PB
DESCRIPTOR.message_types_by_name['SKBuiltinUint32_PB'] = _SKBUILTINUINT32_PB
DESCRIPTOR.message_types_by_name['SKBuiltinChar_PB'] = _SKBUILTINCHAR_PB
DESCRIPTOR.message_types_by_name['SKBuiltinUchar_PB'] = _SKBUILTINUCHAR_PB
DESCRIPTOR.message_types_by_name['SKBuiltinInt8_PB'] = _SKBUILTININT8_PB
DESCRIPTOR.message_types_by_name['SKBuiltinUint8_PB'] = _SKBUILTINUINT8_PB
DESCRIPTOR.message_types_by_name['SKBuiltinInt16_PB'] = _SKBUILTININT16_PB
DESCRIPTOR.message_types_by_name['SKBuiltinUint16_PB'] = _SKBUILTINUINT16_PB
DESCRIPTOR.message_types_by_name['SKBuiltinInt64_PB'] = _SKBUILTININT64_PB
DESCRIPTOR.message_types_by_name['SKBuiltinUint64_PB'] = _SKBUILTINUINT64_PB
DESCRIPTOR.message_types_by_name['SKBuiltinFloat32_PB'] = _SKBUILTINFLOAT32_PB
DESCRIPTOR.message_types_by_name['SKBuiltinDouble64_PB'] = _SKBUILTINDOUBLE64_PB
DESCRIPTOR.message_types_by_name['SKBuiltinBuffer_PB'] = _SKBUILTINBUFFER_PB
DESCRIPTOR.message_types_by_name['SKBuiltinString_PB'] = _SKBUILTINSTRING_PB
DESCRIPTOR.message_types_by_name['SKBuiltinBytes_PB'] = _SKBUILTINBYTES_PB
DESCRIPTOR.message_types_by_name['SKBuiltinEmpty_PB'] = _SKBUILTINEMPTY_PB
DESCRIPTOR.message_types_by_name['SKBuiltinNoexsit_PB'] = _SKBUILTINNOEXSIT_PB
DESCRIPTOR.message_types_by_name['SKBuiltinEchoInfo_PB'] = _SKBUILTINECHOINFO_PB
DESCRIPTOR.message_types_by_name['SKPBMetaInfo'] = _SKPBMETAINFO
DESCRIPTOR.message_types_by_name['SKPBMetaInfoResp'] = _SKPBMETAINFORESP
DESCRIPTOR.message_types_by_name['SKProfileReq'] = _SKPROFILEREQ
DESCRIPTOR.message_types_by_name['SKProfileResp'] = _SKPROFILERESP
DESCRIPTOR.message_types_by_name['SKBuiltinGenericMsg_PB'] = _SKBUILTINGENERICMSG_PB
DESCRIPTOR.message_types_by_name['SKByPassInfo'] = _SKBYPASSINFO
DESCRIPTOR.message_types_by_name['SKReport2WQueueReq'] = _SKREPORT2WQUEUEREQ
DESCRIPTOR.message_types_by_name['SKExtendRet'] = _SKEXTENDRET
DESCRIPTOR.message_types_by_name['SKFieldMap'] = _SKFIELDMAP
DESCRIPTOR.enum_types_by_name['MMClientCallType'] = _MMCLIENTCALLTYPE
DESCRIPTOR.enum_types_by_name['SvrkitProcessType'] = _SVRKITPROCESSTYPE
DESCRIPTOR.extensions_by_name['EnumValueDesc'] = EnumValueDesc
DESCRIPTOR.extensions_by_name['EnumDesc'] = EnumDesc
DESCRIPTOR.extensions_by_name['Desc'] = Desc
DESCRIPTOR.extensions_by_name['Min'] = Min
DESCRIPTOR.extensions_by_name['Max'] = Max
DESCRIPTOR.extensions_by_name['ValueList'] = ValueList
DESCRIPTOR.extensions_by_name['SectionName'] = SectionName
DESCRIPTOR.extensions_by_name['KeyName'] = KeyName
DESCRIPTOR.extensions_by_name['WxUin64Type'] = WxUin64Type
DESCRIPTOR.extensions_by_name['ServerType'] = ServerType
DESCRIPTOR.extensions_by_name['Magic'] = Magic
DESCRIPTOR.extensions_by_name['Target'] = Target
DESCRIPTOR.extensions_by_name['RouteMethod'] = RouteMethod
DESCRIPTOR.extensions_by_name['UseGateway'] = UseGateway
DESCRIPTOR.extensions_by_name['TemplateType'] = TemplateType
DESCRIPTOR.extensions_by_name['MQType'] = MQType
DESCRIPTOR.extensions_by_name['UseWxPayMultiSet'] = UseWxPayMultiSet
DESCRIPTOR.extensions_by_name['EnableAttachment'] = EnableAttachment
DESCRIPTOR.extensions_by_name['ClientAllowDepends'] = ClientAllowDepends
DESCRIPTOR.extensions_by_name['UseMultienv'] = UseMultienv
DESCRIPTOR.extensions_by_name['ServiceRouteMethod'] = ServiceRouteMethod
DESCRIPTOR.extensions_by_name['UinType'] = UinType
DESCRIPTOR.extensions_by_name['ServerUinType'] = ServerUinType
DESCRIPTOR.extensions_by_name['ClientUinType'] = ClientUinType
DESCRIPTOR.extensions_by_name['CmdID'] = CmdID
DESCRIPTOR.extensions_by_name['OptString'] = OptString
DESCRIPTOR.extensions_by_name['Usage'] = Usage
DESCRIPTOR.extensions_by_name['NeedExtEnd'] = NeedExtEnd
DESCRIPTOR.extensions_by_name['ExtendRet'] = ExtendRet
DESCRIPTOR.extensions_by_name['Brief'] = Brief
DESCRIPTOR.extensions_by_name['Url'] = Url
DESCRIPTOR.extensions_by_name['FieldMap'] = FieldMap
DESCRIPTOR.extensions_by_name['RespType'] = RespType
DESCRIPTOR.extensions_by_name['ClientCallType'] = ClientCallType
DESCRIPTOR.extensions_by_name['MQHandlerType'] = MQHandlerType
DESCRIPTOR.extensions_by_name['BusinessErrCode'] = BusinessErrCode
DESCRIPTOR.extensions_by_name['EnableReqValidator'] = EnableReqValidator
DESCRIPTOR.extensions_by_name['ProcessType'] = ProcessType
DESCRIPTOR.extensions_by_name['IsBatchMethod'] = IsBatchMethod
DESCRIPTOR.extensions_by_name['RpcRouteMethod'] = RpcRouteMethod
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

SKBuiltinInt32_PB = _reflection.GeneratedProtocolMessageType('SKBuiltinInt32_PB', (_message.Message,), dict(
  DESCRIPTOR = _SKBUILTININT32_PB,
  __module__ = 'comm2.tlvpickle.skbuiltintype_pb2'
  # @@protoc_insertion_point(class_scope:tlvpickle.SKBuiltinInt32_PB)
  ))
_sym_db.RegisterMessage(SKBuiltinInt32_PB)

SKBuiltinUint32_PB = _reflection.GeneratedProtocolMessageType('SKBuiltinUint32_PB', (_message.Message,), dict(
  DESCRIPTOR = _SKBUILTINUINT32_PB,
  __module__ = 'comm2.tlvpickle.skbuiltintype_pb2'
  # @@protoc_insertion_point(class_scope:tlvpickle.SKBuiltinUint32_PB)
  ))
_sym_db.RegisterMessage(SKBuiltinUint32_PB)

SKBuiltinChar_PB = _reflection.GeneratedProtocolMessageType('SKBuiltinChar_PB', (_message.Message,), dict(
  DESCRIPTOR = _SKBUILTINCHAR_PB,
  __module__ = 'comm2.tlvpickle.skbuiltintype_pb2'
  # @@protoc_insertion_point(class_scope:tlvpickle.SKBuiltinChar_PB)
  ))
_sym_db.RegisterMessage(SKBuiltinChar_PB)

SKBuiltinUchar_PB = _reflection.GeneratedProtocolMessageType('SKBuiltinUchar_PB', (_message.Message,), dict(
  DESCRIPTOR = _SKBUILTINUCHAR_PB,
  __module__ = 'comm2.tlvpickle.skbuiltintype_pb2'
  # @@protoc_insertion_point(class_scope:tlvpickle.SKBuiltinUchar_PB)
  ))
_sym_db.RegisterMessage(SKBuiltinUchar_PB)

SKBuiltinInt8_PB = _reflection.GeneratedProtocolMessageType('SKBuiltinInt8_PB', (_message.Message,), dict(
  DESCRIPTOR = _SKBUILTININT8_PB,
  __module__ = 'comm2.tlvpickle.skbuiltintype_pb2'
  # @@protoc_insertion_point(class_scope:tlvpickle.SKBuiltinInt8_PB)
  ))
_sym_db.RegisterMessage(SKBuiltinInt8_PB)

SKBuiltinUint8_PB = _reflection.GeneratedProtocolMessageType('SKBuiltinUint8_PB', (_message.Message,), dict(
  DESCRIPTOR = _SKBUILTINUINT8_PB,
  __module__ = 'comm2.tlvpickle.skbuiltintype_pb2'
  # @@protoc_insertion_point(class_scope:tlvpickle.SKBuiltinUint8_PB)
  ))
_sym_db.RegisterMessage(SKBuiltinUint8_PB)

SKBuiltinInt16_PB = _reflection.GeneratedProtocolMessageType('SKBuiltinInt16_PB', (_message.Message,), dict(
  DESCRIPTOR = _SKBUILTININT16_PB,
  __module__ = 'comm2.tlvpickle.skbuiltintype_pb2'
  # @@protoc_insertion_point(class_scope:tlvpickle.SKBuiltinInt16_PB)
  ))
_sym_db.RegisterMessage(SKBuiltinInt16_PB)

SKBuiltinUint16_PB = _reflection.GeneratedProtocolMessageType('SKBuiltinUint16_PB', (_message.Message,), dict(
  DESCRIPTOR = _SKBUILTINUINT16_PB,
  __module__ = 'comm2.tlvpickle.skbuiltintype_pb2'
  # @@protoc_insertion_point(class_scope:tlvpickle.SKBuiltinUint16_PB)
  ))
_sym_db.RegisterMessage(SKBuiltinUint16_PB)

SKBuiltinInt64_PB = _reflection.GeneratedProtocolMessageType('SKBuiltinInt64_PB', (_message.Message,), dict(
  DESCRIPTOR = _SKBUILTININT64_PB,
  __module__ = 'comm2.tlvpickle.skbuiltintype_pb2'
  # @@protoc_insertion_point(class_scope:tlvpickle.SKBuiltinInt64_PB)
  ))
_sym_db.RegisterMessage(SKBuiltinInt64_PB)

SKBuiltinUint64_PB = _reflection.GeneratedProtocolMessageType('SKBuiltinUint64_PB', (_message.Message,), dict(
  DESCRIPTOR = _SKBUILTINUINT64_PB,
  __module__ = 'comm2.tlvpickle.skbuiltintype_pb2'
  # @@protoc_insertion_point(class_scope:tlvpickle.SKBuiltinUint64_PB)
  ))
_sym_db.RegisterMessage(SKBuiltinUint64_PB)

SKBuiltinFloat32_PB = _reflection.GeneratedProtocolMessageType('SKBuiltinFloat32_PB', (_message.Message,), dict(
  DESCRIPTOR = _SKBUILTINFLOAT32_PB,
  __module__ = 'comm2.tlvpickle.skbuiltintype_pb2'
  # @@protoc_insertion_point(class_scope:tlvpickle.SKBuiltinFloat32_PB)
  ))
_sym_db.RegisterMessage(SKBuiltinFloat32_PB)

SKBuiltinDouble64_PB = _reflection.GeneratedProtocolMessageType('SKBuiltinDouble64_PB', (_message.Message,), dict(
  DESCRIPTOR = _SKBUILTINDOUBLE64_PB,
  __module__ = 'comm2.tlvpickle.skbuiltintype_pb2'
  # @@protoc_insertion_point(class_scope:tlvpickle.SKBuiltinDouble64_PB)
  ))
_sym_db.RegisterMessage(SKBuiltinDouble64_PB)

SKBuiltinBuffer_PB = _reflection.GeneratedProtocolMessageType('SKBuiltinBuffer_PB', (_message.Message,), dict(
  DESCRIPTOR = _SKBUILTINBUFFER_PB,
  __module__ = 'comm2.tlvpickle.skbuiltintype_pb2'
  # @@protoc_insertion_point(class_scope:tlvpickle.SKBuiltinBuffer_PB)
  ))
_sym_db.RegisterMessage(SKBuiltinBuffer_PB)

SKBuiltinString_PB = _reflection.GeneratedProtocolMessageType('SKBuiltinString_PB', (_message.Message,), dict(
  DESCRIPTOR = _SKBUILTINSTRING_PB,
  __module__ = 'comm2.tlvpickle.skbuiltintype_pb2'
  # @@protoc_insertion_point(class_scope:tlvpickle.SKBuiltinString_PB)
  ))
_sym_db.RegisterMessage(SKBuiltinString_PB)

SKBuiltinBytes_PB = _reflection.GeneratedProtocolMessageType('SKBuiltinBytes_PB', (_message.Message,), dict(
  DESCRIPTOR = _SKBUILTINBYTES_PB,
  __module__ = 'comm2.tlvpickle.skbuiltintype_pb2'
  # @@protoc_insertion_point(class_scope:tlvpickle.SKBuiltinBytes_PB)
  ))
_sym_db.RegisterMessage(SKBuiltinBytes_PB)

SKBuiltinEmpty_PB = _reflection.GeneratedProtocolMessageType('SKBuiltinEmpty_PB', (_message.Message,), dict(
  DESCRIPTOR = _SKBUILTINEMPTY_PB,
  __module__ = 'comm2.tlvpickle.skbuiltintype_pb2'
  # @@protoc_insertion_point(class_scope:tlvpickle.SKBuiltinEmpty_PB)
  ))
_sym_db.RegisterMessage(SKBuiltinEmpty_PB)

SKBuiltinNoexsit_PB = _reflection.GeneratedProtocolMessageType('SKBuiltinNoexsit_PB', (_message.Message,), dict(
  DESCRIPTOR = _SKBUILTINNOEXSIT_PB,
  __module__ = 'comm2.tlvpickle.skbuiltintype_pb2'
  # @@protoc_insertion_point(class_scope:tlvpickle.SKBuiltinNoexsit_PB)
  ))
_sym_db.RegisterMessage(SKBuiltinNoexsit_PB)

SKBuiltinEchoInfo_PB = _reflection.GeneratedProtocolMessageType('SKBuiltinEchoInfo_PB', (_message.Message,), dict(
  DESCRIPTOR = _SKBUILTINECHOINFO_PB,
  __module__ = 'comm2.tlvpickle.skbuiltintype_pb2'
  # @@protoc_insertion_point(class_scope:tlvpickle.SKBuiltinEchoInfo_PB)
  ))
_sym_db.RegisterMessage(SKBuiltinEchoInfo_PB)

SKPBMetaInfo = _reflection.GeneratedProtocolMessageType('SKPBMetaInfo', (_message.Message,), dict(
  DESCRIPTOR = _SKPBMETAINFO,
  __module__ = 'comm2.tlvpickle.skbuiltintype_pb2'
  # @@protoc_insertion_point(class_scope:tlvpickle.SKPBMetaInfo)
  ))
_sym_db.RegisterMessage(SKPBMetaInfo)

SKPBMetaInfoResp = _reflection.GeneratedProtocolMessageType('SKPBMetaInfoResp', (_message.Message,), dict(
  DESCRIPTOR = _SKPBMETAINFORESP,
  __module__ = 'comm2.tlvpickle.skbuiltintype_pb2'
  # @@protoc_insertion_point(class_scope:tlvpickle.SKPBMetaInfoResp)
  ))
_sym_db.RegisterMessage(SKPBMetaInfoResp)

SKProfileReq = _reflection.GeneratedProtocolMessageType('SKProfileReq', (_message.Message,), dict(
  DESCRIPTOR = _SKPROFILEREQ,
  __module__ = 'comm2.tlvpickle.skbuiltintype_pb2'
  # @@protoc_insertion_point(class_scope:tlvpickle.SKProfileReq)
  ))
_sym_db.RegisterMessage(SKProfileReq)

SKProfileResp = _reflection.GeneratedProtocolMessageType('SKProfileResp', (_message.Message,), dict(
  DESCRIPTOR = _SKPROFILERESP,
  __module__ = 'comm2.tlvpickle.skbuiltintype_pb2'
  # @@protoc_insertion_point(class_scope:tlvpickle.SKProfileResp)
  ))
_sym_db.RegisterMessage(SKProfileResp)

SKBuiltinGenericMsg_PB = _reflection.GeneratedProtocolMessageType('SKBuiltinGenericMsg_PB', (_message.Message,), dict(
  DESCRIPTOR = _SKBUILTINGENERICMSG_PB,
  __module__ = 'comm2.tlvpickle.skbuiltintype_pb2'
  # @@protoc_insertion_point(class_scope:tlvpickle.SKBuiltinGenericMsg_PB)
  ))
_sym_db.RegisterMessage(SKBuiltinGenericMsg_PB)

SKByPassInfo = _reflection.GeneratedProtocolMessageType('SKByPassInfo', (_message.Message,), dict(
  DESCRIPTOR = _SKBYPASSINFO,
  __module__ = 'comm2.tlvpickle.skbuiltintype_pb2'
  # @@protoc_insertion_point(class_scope:tlvpickle.SKByPassInfo)
  ))
_sym_db.RegisterMessage(SKByPassInfo)

SKReport2WQueueReq = _reflection.GeneratedProtocolMessageType('SKReport2WQueueReq', (_message.Message,), dict(
  DESCRIPTOR = _SKREPORT2WQUEUEREQ,
  __module__ = 'comm2.tlvpickle.skbuiltintype_pb2'
  # @@protoc_insertion_point(class_scope:tlvpickle.SKReport2WQueueReq)
  ))
_sym_db.RegisterMessage(SKReport2WQueueReq)

SKExtendRet = _reflection.GeneratedProtocolMessageType('SKExtendRet', (_message.Message,), dict(
  DESCRIPTOR = _SKEXTENDRET,
  __module__ = 'comm2.tlvpickle.skbuiltintype_pb2'
  # @@protoc_insertion_point(class_scope:tlvpickle.SKExtendRet)
  ))
_sym_db.RegisterMessage(SKExtendRet)

SKFieldMap = _reflection.GeneratedProtocolMessageType('SKFieldMap', (_message.Message,), dict(
  DESCRIPTOR = _SKFIELDMAP,
  __module__ = 'comm2.tlvpickle.skbuiltintype_pb2'
  # @@protoc_insertion_point(class_scope:tlvpickle.SKFieldMap)
  ))
_sym_db.RegisterMessage(SKFieldMap)

google_dot_protobuf_dot_descriptor__pb2.EnumValueOptions.RegisterExtension(EnumValueDesc)
google_dot_protobuf_dot_descriptor__pb2.EnumOptions.RegisterExtension(EnumDesc)
google_dot_protobuf_dot_descriptor__pb2.FieldOptions.RegisterExtension(Desc)
google_dot_protobuf_dot_descriptor__pb2.FieldOptions.RegisterExtension(Min)
google_dot_protobuf_dot_descriptor__pb2.FieldOptions.RegisterExtension(Max)
google_dot_protobuf_dot_descriptor__pb2.FieldOptions.RegisterExtension(ValueList)
google_dot_protobuf_dot_descriptor__pb2.FieldOptions.RegisterExtension(SectionName)
google_dot_protobuf_dot_descriptor__pb2.FieldOptions.RegisterExtension(KeyName)
google_dot_protobuf_dot_descriptor__pb2.FieldOptions.RegisterExtension(WxUin64Type)
google_dot_protobuf_dot_descriptor__pb2.ServiceOptions.RegisterExtension(ServerType)
google_dot_protobuf_dot_descriptor__pb2.ServiceOptions.RegisterExtension(Magic)
google_dot_protobuf_dot_descriptor__pb2.ServiceOptions.RegisterExtension(Target)
google_dot_protobuf_dot_descriptor__pb2.ServiceOptions.RegisterExtension(RouteMethod)
google_dot_protobuf_dot_descriptor__pb2.ServiceOptions.RegisterExtension(UseGateway)
google_dot_protobuf_dot_descriptor__pb2.ServiceOptions.RegisterExtension(TemplateType)
google_dot_protobuf_dot_descriptor__pb2.ServiceOptions.RegisterExtension(MQType)
google_dot_protobuf_dot_descriptor__pb2.ServiceOptions.RegisterExtension(UseWxPayMultiSet)
google_dot_protobuf_dot_descriptor__pb2.ServiceOptions.RegisterExtension(EnableAttachment)
google_dot_protobuf_dot_descriptor__pb2.ServiceOptions.RegisterExtension(ClientAllowDepends)
google_dot_protobuf_dot_descriptor__pb2.ServiceOptions.RegisterExtension(UseMultienv)
google_dot_protobuf_dot_descriptor__pb2.ServiceOptions.RegisterExtension(ServiceRouteMethod)
google_dot_protobuf_dot_descriptor__pb2.ServiceOptions.RegisterExtension(UinType)
google_dot_protobuf_dot_descriptor__pb2.ServiceOptions.RegisterExtension(ServerUinType)
google_dot_protobuf_dot_descriptor__pb2.ServiceOptions.RegisterExtension(ClientUinType)
google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(CmdID)
google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(OptString)
google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(Usage)
google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(NeedExtEnd)
ExtendRet.message_type = _SKEXTENDRET
google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(ExtendRet)
google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(Brief)
google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(Url)
FieldMap.message_type = _SKFIELDMAP
google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(FieldMap)
google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(RespType)
ClientCallType.enum_type = _MMCLIENTCALLTYPE
google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(ClientCallType)
google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(MQHandlerType)
google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(BusinessErrCode)
google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(EnableReqValidator)
ProcessType.enum_type = _SVRKITPROCESSTYPE
google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(ProcessType)
google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(IsBatchMethod)
google_dot_protobuf_dot_descriptor__pb2.MethodOptions.RegisterExtension(RpcRouteMethod)

DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)
