# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mmfinderdrallinonehttpsvr.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from comm2.tlvpickle import skbuiltintype_pb2 as comm2_dot_tlvpickle_dot_skbuiltintype__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='mmfinderdrallinonehttpsvr.proto',
  package='mmfinderdrallinonehttpsvr',
  syntax='proto2',
  serialized_options=None,
  serialized_pb=_b('\n\x1fmmfinderdrallinonehttpsvr.proto\x12\x19mmfinderdrallinonehttpsvr\x1a#comm2/tlvpickle/skbuiltintype.proto\"$\n\x06Header\x12\x0b\n\x03key\x18\x01 \x02(\t\x12\r\n\x05value\x18\x02 \x02(\t\"(\n\nQueryParam\x12\x0b\n\x03key\x18\x01 \x02(\t\x12\r\n\x05value\x18\x02 \x02(\t\"\x18\n\tPathParam\x12\x0b\n\x03key\x18\x01 \x02(\t\"$\n\x06\x43ookie\x12\x0b\n\x03key\x18\x01 \x02(\t\x12\r\n\x05value\x18\x02 \x02(\t\"\xf9\x01\n\tCommonReq\x12\x32\n\x07headers\x18\x01 \x03(\x0b\x32!.mmfinderdrallinonehttpsvr.Header\x12\x32\n\x07\x63ookies\x18\x02 \x03(\x0b\x32!.mmfinderdrallinonehttpsvr.Cookie\x12\x0c\n\x04\x64\x61ta\x18\x03 \x01(\x0c\x12;\n\x0cquery_params\x18\x04 \x03(\x0b\x32%.mmfinderdrallinonehttpsvr.QueryParam\x12\x39\n\x0bpath_params\x18\x05 \x03(\x0b\x32$.mmfinderdrallinonehttpsvr.PathParam\"\xb3\x01\n\nCommonResp\x12\x13\n\x0bstatus_code\x18\x01 \x02(\x05\x12\x0e\n\x06status\x18\x02 \x02(\t\x12\x32\n\x07headers\x18\x03 \x03(\x0b\x32!.mmfinderdrallinonehttpsvr.Header\x12\x32\n\x07\x63ookies\x18\x04 \x03(\x0b\x32!.mmfinderdrallinonehttpsvr.Cookie\x12\x0c\n\x04\x64\x61ta\x18\x05 \x01(\x0c\x12\n\n\x02ip\x18\x06 \x01(\t2\x8e\x06\n\x19MmFinderDrAllInOneHttpSvr\x12u\n\x0bRunWorkflow\x12$.mmfinderdrallinonehttpsvr.CommonReq\x1a%.mmfinderdrallinonehttpsvr.CommonResp\"\x19\x80\xa4\xe8\x03\x01\x8a\xa4\xe8\x03\x02s:\x92\xa4\xe8\x03\x08-s <msg>\x12v\n\x0c\x43hatMessages\x12$.mmfinderdrallinonehttpsvr.CommonReq\x1a%.mmfinderdrallinonehttpsvr.CommonResp\"\x19\x80\xa4\xe8\x03\x02\x8a\xa4\xe8\x03\x02s:\x92\xa4\xe8\x03\x08-s <msg>\x12y\n\x0fGetConversation\x12$.mmfinderdrallinonehttpsvr.CommonReq\x1a%.mmfinderdrallinonehttpsvr.CommonResp\"\x19\x80\xa4\xe8\x03\x03\x8a\xa4\xe8\x03\x02s:\x92\xa4\xe8\x03\x08-s <msg>\x12|\n\x12\x44\x65leteConversation\x12$.mmfinderdrallinonehttpsvr.CommonReq\x1a%.mmfinderdrallinonehttpsvr.CommonResp\"\x19\x80\xa4\xe8\x03\x04\x8a\xa4\xe8\x03\x02s:\x92\xa4\xe8\x03\x08-s <msg>\x12q\n\x07GetInfo\x12$.mmfinderdrallinonehttpsvr.CommonReq\x1a%.mmfinderdrallinonehttpsvr.CommonResp\"\x19\x80\xa4\xe8\x03\x05\x8a\xa4\xe8\x03\x02s:\x92\xa4\xe8\x03\x08-s <msg>\x12\x84\x01\n\x15RunWorkflowFastReturn\x12$.mmfinderdrallinonehttpsvr.CommonReq\x1a%.mmfinderdrallinonehttpsvr.CommonResp\"\x1e\x80\xa4\xe8\x03\x06\x8a\xa4\xe8\x03\x02s:\x92\xa4\xe8\x03\x08-s <msg>\xc0\xa4\xe8\x03\x03\x1a\x0f\x88\xa4\xe8\x03\xeb\xa9\x01\x82\xa4\xe8\x03\x03TCP')
  ,
  dependencies=[comm2_dot_tlvpickle_dot_skbuiltintype__pb2.DESCRIPTOR,])




_HEADER = _descriptor.Descriptor(
  name='Header',
  full_name='mmfinderdrallinonehttpsvr.Header',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='mmfinderdrallinonehttpsvr.Header.key', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='mmfinderdrallinonehttpsvr.Header.value', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=99,
  serialized_end=135,
)


_QUERYPARAM = _descriptor.Descriptor(
  name='QueryParam',
  full_name='mmfinderdrallinonehttpsvr.QueryParam',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='mmfinderdrallinonehttpsvr.QueryParam.key', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='mmfinderdrallinonehttpsvr.QueryParam.value', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=137,
  serialized_end=177,
)


_PATHPARAM = _descriptor.Descriptor(
  name='PathParam',
  full_name='mmfinderdrallinonehttpsvr.PathParam',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='mmfinderdrallinonehttpsvr.PathParam.key', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=179,
  serialized_end=203,
)


_COOKIE = _descriptor.Descriptor(
  name='Cookie',
  full_name='mmfinderdrallinonehttpsvr.Cookie',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='mmfinderdrallinonehttpsvr.Cookie.key', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='mmfinderdrallinonehttpsvr.Cookie.value', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=205,
  serialized_end=241,
)


_COMMONREQ = _descriptor.Descriptor(
  name='CommonReq',
  full_name='mmfinderdrallinonehttpsvr.CommonReq',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='headers', full_name='mmfinderdrallinonehttpsvr.CommonReq.headers', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cookies', full_name='mmfinderdrallinonehttpsvr.CommonReq.cookies', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='data', full_name='mmfinderdrallinonehttpsvr.CommonReq.data', index=2,
      number=3, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='query_params', full_name='mmfinderdrallinonehttpsvr.CommonReq.query_params', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='path_params', full_name='mmfinderdrallinonehttpsvr.CommonReq.path_params', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=244,
  serialized_end=493,
)


_COMMONRESP = _descriptor.Descriptor(
  name='CommonResp',
  full_name='mmfinderdrallinonehttpsvr.CommonResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='status_code', full_name='mmfinderdrallinonehttpsvr.CommonResp.status_code', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mmfinderdrallinonehttpsvr.CommonResp.status', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='headers', full_name='mmfinderdrallinonehttpsvr.CommonResp.headers', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cookies', full_name='mmfinderdrallinonehttpsvr.CommonResp.cookies', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='data', full_name='mmfinderdrallinonehttpsvr.CommonResp.data', index=4,
      number=5, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ip', full_name='mmfinderdrallinonehttpsvr.CommonResp.ip', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=496,
  serialized_end=675,
)

_COMMONREQ.fields_by_name['headers'].message_type = _HEADER
_COMMONREQ.fields_by_name['cookies'].message_type = _COOKIE
_COMMONREQ.fields_by_name['query_params'].message_type = _QUERYPARAM
_COMMONREQ.fields_by_name['path_params'].message_type = _PATHPARAM
_COMMONRESP.fields_by_name['headers'].message_type = _HEADER
_COMMONRESP.fields_by_name['cookies'].message_type = _COOKIE
DESCRIPTOR.message_types_by_name['Header'] = _HEADER
DESCRIPTOR.message_types_by_name['QueryParam'] = _QUERYPARAM
DESCRIPTOR.message_types_by_name['PathParam'] = _PATHPARAM
DESCRIPTOR.message_types_by_name['Cookie'] = _COOKIE
DESCRIPTOR.message_types_by_name['CommonReq'] = _COMMONREQ
DESCRIPTOR.message_types_by_name['CommonResp'] = _COMMONRESP
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Header = _reflection.GeneratedProtocolMessageType('Header', (_message.Message,), dict(
  DESCRIPTOR = _HEADER,
  __module__ = 'mmfinderdrallinonehttpsvr_pb2'
  # @@protoc_insertion_point(class_scope:mmfinderdrallinonehttpsvr.Header)
  ))
_sym_db.RegisterMessage(Header)

QueryParam = _reflection.GeneratedProtocolMessageType('QueryParam', (_message.Message,), dict(
  DESCRIPTOR = _QUERYPARAM,
  __module__ = 'mmfinderdrallinonehttpsvr_pb2'
  # @@protoc_insertion_point(class_scope:mmfinderdrallinonehttpsvr.QueryParam)
  ))
_sym_db.RegisterMessage(QueryParam)

PathParam = _reflection.GeneratedProtocolMessageType('PathParam', (_message.Message,), dict(
  DESCRIPTOR = _PATHPARAM,
  __module__ = 'mmfinderdrallinonehttpsvr_pb2'
  # @@protoc_insertion_point(class_scope:mmfinderdrallinonehttpsvr.PathParam)
  ))
_sym_db.RegisterMessage(PathParam)

Cookie = _reflection.GeneratedProtocolMessageType('Cookie', (_message.Message,), dict(
  DESCRIPTOR = _COOKIE,
  __module__ = 'mmfinderdrallinonehttpsvr_pb2'
  # @@protoc_insertion_point(class_scope:mmfinderdrallinonehttpsvr.Cookie)
  ))
_sym_db.RegisterMessage(Cookie)

CommonReq = _reflection.GeneratedProtocolMessageType('CommonReq', (_message.Message,), dict(
  DESCRIPTOR = _COMMONREQ,
  __module__ = 'mmfinderdrallinonehttpsvr_pb2'
  # @@protoc_insertion_point(class_scope:mmfinderdrallinonehttpsvr.CommonReq)
  ))
_sym_db.RegisterMessage(CommonReq)

CommonResp = _reflection.GeneratedProtocolMessageType('CommonResp', (_message.Message,), dict(
  DESCRIPTOR = _COMMONRESP,
  __module__ = 'mmfinderdrallinonehttpsvr_pb2'
  # @@protoc_insertion_point(class_scope:mmfinderdrallinonehttpsvr.CommonResp)
  ))
_sym_db.RegisterMessage(CommonResp)



_MMFINDERDRALLINONEHTTPSVR = _descriptor.ServiceDescriptor(
  name='MmFinderDrAllInOneHttpSvr',
  full_name='mmfinderdrallinonehttpsvr.MmFinderDrAllInOneHttpSvr',
  file=DESCRIPTOR,
  index=0,
  serialized_options=_b('\210\244\350\003\353\251\001\202\244\350\003\003TCP'),
  serialized_start=678,
  serialized_end=1460,
  methods=[
  _descriptor.MethodDescriptor(
    name='RunWorkflow',
    full_name='mmfinderdrallinonehttpsvr.MmFinderDrAllInOneHttpSvr.RunWorkflow',
    index=0,
    containing_service=None,
    input_type=_COMMONREQ,
    output_type=_COMMONRESP,
    serialized_options=_b('\200\244\350\003\001\212\244\350\003\002s:\222\244\350\003\010-s <msg>'),
  ),
  _descriptor.MethodDescriptor(
    name='ChatMessages',
    full_name='mmfinderdrallinonehttpsvr.MmFinderDrAllInOneHttpSvr.ChatMessages',
    index=1,
    containing_service=None,
    input_type=_COMMONREQ,
    output_type=_COMMONRESP,
    serialized_options=_b('\200\244\350\003\002\212\244\350\003\002s:\222\244\350\003\010-s <msg>'),
  ),
  _descriptor.MethodDescriptor(
    name='GetConversation',
    full_name='mmfinderdrallinonehttpsvr.MmFinderDrAllInOneHttpSvr.GetConversation',
    index=2,
    containing_service=None,
    input_type=_COMMONREQ,
    output_type=_COMMONRESP,
    serialized_options=_b('\200\244\350\003\003\212\244\350\003\002s:\222\244\350\003\010-s <msg>'),
  ),
  _descriptor.MethodDescriptor(
    name='DeleteConversation',
    full_name='mmfinderdrallinonehttpsvr.MmFinderDrAllInOneHttpSvr.DeleteConversation',
    index=3,
    containing_service=None,
    input_type=_COMMONREQ,
    output_type=_COMMONRESP,
    serialized_options=_b('\200\244\350\003\004\212\244\350\003\002s:\222\244\350\003\010-s <msg>'),
  ),
  _descriptor.MethodDescriptor(
    name='GetInfo',
    full_name='mmfinderdrallinonehttpsvr.MmFinderDrAllInOneHttpSvr.GetInfo',
    index=4,
    containing_service=None,
    input_type=_COMMONREQ,
    output_type=_COMMONRESP,
    serialized_options=_b('\200\244\350\003\005\212\244\350\003\002s:\222\244\350\003\010-s <msg>'),
  ),
  _descriptor.MethodDescriptor(
    name='RunWorkflowFastReturn',
    full_name='mmfinderdrallinonehttpsvr.MmFinderDrAllInOneHttpSvr.RunWorkflowFastReturn',
    index=5,
    containing_service=None,
    input_type=_COMMONREQ,
    output_type=_COMMONRESP,
    serialized_options=_b('\200\244\350\003\006\212\244\350\003\002s:\222\244\350\003\010-s <msg>\300\244\350\003\003'),
  ),
])
_sym_db.RegisterServiceDescriptor(_MMFINDERDRALLINONEHTTPSVR)

DESCRIPTOR.services_by_name['MmFinderDrAllInOneHttpSvr'] = _MMFINDERDRALLINONEHTTPSVR

# @@protoc_insertion_point(module_scope)
