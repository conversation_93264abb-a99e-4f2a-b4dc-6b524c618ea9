provider: openai_api_compatible
label:
  en_US: OpenAI-API-compatible
description:
  en_US: Model providers compatible with OpenAI's API standard, such as LM Studio.
  zh_Hans: 兼容 OpenAI API 的模型供应商，例如 LM Studio 。
supported_model_types:
  - llm
  - text-embedding
  - speech2text
  - rerank
  - tts
configurate_methods:
  - customizable-model
model_credential_schema:
  model:
    label:
      en_US: Model Name
      zh_Hans: 模型名称
    placeholder:
      en_US: Enter full model name
      zh_Hans: 输入模型全称
  credential_form_schemas:
    - variable: api_key
      label:
        en_US: API Key(useless)
      type: text-input
      required: false
      placeholder:
        zh_Hans: 在此输入您的 API Key
        en_US: Enter your API Key
    - variable: endpoint_url
      label:
        zh_Hans: 内网接口url
        en_US: API endpoint URL
      type: text-input
      required: true
      placeholder:
        zh_Hans: Base URL, e.g. https://api.openai.com/v1
        en_US: Base URL, e.g. https://api.openai.com/v1
    - variable: mode
      show_on:
        - variable: __model_type
          value: llm
      label:
        en_US: Completion mode
      type: select
      required: false
      default: chat
      placeholder:
        zh_Hans: 选择对话类型
        en_US: Select completion mode
      options:
        - value: completion
          label:
            en_US: Completion
            zh_Hans: 补全
        - value: chat
          label:
            en_US: Chat
            zh_Hans: 对话
    - variable: context_size
      label:
        zh_Hans: 模型上下文长度
        en_US: Model context size
      required: true
      show_on:
        - variable: __model_type
          value: llm
      type: text-input
      default: "4096"
      placeholder:
        zh_Hans: 在此输入您的模型上下文长度
        en_US: Enter your Model context size
    - variable: context_size
      label:
        zh_Hans: 模型上下文长度
        en_US: Model context size
      required: true
      show_on:
        - variable: __model_type
          value: text-embedding
      type: text-input
      default: "4096"
      placeholder:
        zh_Hans: 在此输入您的模型上下文长度
        en_US: Enter your Model context size
    - variable: context_size
      label:
        zh_Hans: 模型上下文长度
        en_US: Model context size
      required: true
      show_on:
        - variable: __model_type
          value: rerank
      type: text-input
      default: "4096"
      placeholder:
        zh_Hans: 在此输入您的模型上下文长度
        en_US: Enter your Model context size
    - variable: max_tokens_to_sample
      label:
        zh_Hans: 最大 token 上限
        en_US: Upper bound for max tokens
      show_on:
        - variable: __model_type
          value: llm
      default: "4096"
      type: text-input
    - variable: function_calling_type
      show_on:
        - variable: __model_type
          value: llm
      label:
        en_US: Function calling
      type: select
      required: false
      default: no_call
      options:
        - value: function_call
          label:
            en_US: Function Call
            zh_Hans: Function Call
        - value: tool_call
          label:
            en_US: Tool Call
            zh_Hans: Tool Call
        - value: no_call
          label:
            en_US: Not Support
            zh_Hans: 不支持
    - variable: stream_function_calling
      show_on:
        - variable: __model_type
          value: llm
      label:
        en_US: Stream function calling
      type: select
      required: false
      default: not_supported
      options:
        - value: supported
          label:
            en_US: Support
            zh_Hans: 支持
        - value: not_supported
          label:
            en_US: Not Support
            zh_Hans: 不支持
    - variable: vision_support
      show_on:
        - variable: __model_type
          value: llm
      label:
        zh_Hans: Vision 支持
        en_US: Vision Support
      type: select
      required: false
      default: no_support
      options:
        - value: support
          label:
            en_US: Support
            zh_Hans: 支持
        - value: no_support
          label:
            en_US: Not Support
            zh_Hans: 不支持
    - variable: stream_mode_delimiter
      label:
        zh_Hans: 流模式返回结果的分隔符
        en_US: Delimiter for streaming results
      show_on:
        - variable: __model_type
          value: llm
      default: '\n\n'
      type: text-input
    - variable: voices
      show_on:
        - variable: __model_type
          value: tts
      label:
        en_US: Available Voices (comma-separated)
        zh_Hans: 可用声音（用英文逗号分隔）
      type: text-input
      required: false
      default: "alloy"
      placeholder:
        en_US: "alloy,echo,fable,onyx,nova,shimmer"
        zh_Hans: "alloy,echo,fable,onyx,nova,shimmer"
      help:
        en_US: "List voice names separated by commas. First voice will be used as default."
        zh_Hans: "用英文逗号分隔的声音列表。第一个声音将作为默认值。"
