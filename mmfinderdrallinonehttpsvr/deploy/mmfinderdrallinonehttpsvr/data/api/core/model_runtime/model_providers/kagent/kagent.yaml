provider: kagent
label:
  en_US: kagent
  zh_Hans:  长得像大模型的agent(流式)
description:
  en_US: call agent like llm 
  zh_Hans: 长得像大模型的agent(流式)
icon_small:
  en_US: icon_s_en.svg
icon_large:
  en_US: icon_l_en.svg
background: "#c0cdff"
help:
  title:
    en_US: appid-xxx-xxx
    zh_Hans:  随便写无所谓
  url:
    en_US: https://platform.kagent.com/api_keys
supported_model_types:
  - llm
configurate_methods:
  - customizable-model
model_credential_schema:
  model:
    label:
      en_US: Model Name
      zh_Hans: 模型名称
    placeholder:
      en_US: Enter your model name
      zh_Hans: 输入模型名称
  credential_form_schemas:
    - variable: api_key
      label:
        en_US: API Key
      type: text-input
      required: true
      placeholder:
        zh_Hans: 在此输入您的 API Key
        en_US: Enter your API Key
    - variable: endpoint_url
      label:
        zh_Hans: 自定义 API endpoint 地址
        en_US: Custom API endpoint URL
      type: text-input
      required: false
      placeholder:
        zh_Hans: Base URL, e.g. https://api.kagent.com/v1 or https://api.kagent.com
        en_US: Base URL, e.g. https://api.kagent.com/v1 or https://api.kagent.com
