provider: ollama
label:
  en_US: Ollama
icon_large:
  en_US: icon_l_en.svg
icon_small:
  en_US: icon_s_en.svg
background: "#F9FAFB"
help:
  title:
    en_US: How to integrate with Ollama
    zh_Hans: 如何集成 Ollama
  url:
    en_US: https://docs.dify.ai/tutorials/model-configuration/ollama
supported_model_types:
  - llm
  - text-embedding
configurate_methods:
  - customizable-model
model_credential_schema:
  model:
    label:
      en_US: Model Name
      zh_Hans: 模型名称
    placeholder:
      en_US: Enter your model name
      zh_Hans: 输入模型名称
  credential_form_schemas:
    - variable: base_url
      label:
        zh_Hans: 基础 URL
        en_US: Base URL
      type: text-input
      required: true
      placeholder:
        zh_Hans: Ollama server 的基础 URL，例如 http://mmfinderdrassistantsvr.prod.polaris:29929
        en_US: Base url of Ollama server, e.g. http://mmfinderdrassistantsvr.prod.polaris:29929
    - variable: mode
      show_on:
        - variable: __model_type
          value: llm
      label:
        zh_Hans: 模型类型
        en_US: Completion mode
      type: select
      required: true
      default: chat
      placeholder:
        zh_Hans: 选择对话类型
        en_US: Select completion mode
      options:
        - value: completion
          label:
            en_US: Completion
            zh_Hans: 补全
        - value: chat
          label:
            en_US: Chat
            zh_Hans: 对话
    - variable: context_size
      label:
        zh_Hans: 模型上下文长度
        en_US: Model context size
      required: true
      type: text-input
      default: '4096'
      placeholder:
        zh_Hans: 在此输入您的模型上下文长度
        en_US: Enter your Model context size
    - variable: max_tokens
      label:
        zh_Hans: 最大 token 上限
        en_US: Upper bound for max tokens
      show_on:
        - variable: __model_type
          value: llm
      default: '4096'
      type: text-input
      required: true
    - variable: vision_support
      label:
        zh_Hans: 是否支持 Vision
        en_US: Vision support
      show_on:
        - variable: __model_type
          value: llm
      default: 'false'
      type: radio
      required: false
      options:
        - value: 'true'
          label:
            en_US: 'Yes'
            zh_Hans: 是
        - value: 'false'
          label:
            en_US: 'No'
            zh_Hans: 否
    - variable: function_call_support
      label:
        zh_Hans: 是否支持函数调用
        en_US: Function call support
      show_on:
        - variable: __model_type
          value: llm
      default: 'false'
      type: radio
      required: false
      options:
        - value: 'true'
          label:
            en_US: 'Yes'
            zh_Hans: 是
        - value: 'false'
          label:
            en_US: 'No'
            zh_Hans: 否
