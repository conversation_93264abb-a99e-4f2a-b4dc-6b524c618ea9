provider: xinference
label:
  en_US: Xorbits Inference
icon_small:
  en_US: icon_s_en.svg
icon_large:
  en_US: icon_l_en.svg
background: "#FAF5FF"
help:
  title:
    en_US: How to deploy Xinference
    zh_Hans: 如何部署 Xinference
  url:
    en_US: https://github.com/xorbitsai/inference
supported_model_types:
  - llm
  - text-embedding
  - rerank
  - speech2text
  - tts
configurate_methods:
  - customizable-model
model_credential_schema:
  model:
    label:
      en_US: Model Name
      zh_Hans: 模型名称
    placeholder:
      en_US: Enter your model name
      zh_Hans: 输入模型名称
  credential_form_schemas:
    - variable: server_url
      label:
        zh_Hans: 服务器URL
        en_US: Server url
      type: text-input
      required: true
      placeholder:
        zh_Hans: 在此输入Xinference的服务器地址，如 http://192.168.1.100:9997
        en_US: Enter the url of your Xinference, e.g. http://192.168.1.100:9997
    - variable: model_uid
      label:
        zh_<PERSON>: 模型UID
        en_US: Model uid
      type: text-input
      required: true
      placeholder:
        zh_Hans: 在此输入您的Model UID
        en_US: Enter the model uid
    - variable: api_key
      label:
        zh_Hans: API密钥
        en_US: API key
      type: text-input
      required: false
      placeholder:
        zh_Hans: 在此输入您的API密钥
        en_US: Enter the api key
    - variable: invoke_timeout
      label:
        zh_Hans: 调用超时时间 (单位:秒)
        en_US: invoke timeout (unit:second)
      type: text-input
      required: true
      default: '60'
      placeholder:
        zh_Hans: 在此输入调用超时时间
        en_US: Enter invoke timeout value
    - variable: max_retries
      label:
        zh_Hans: 调用重试次数
        en_US: max retries
      type: text-input
      required: true
      default: '3'
      placeholder:
        zh_Hans: 在此输入调用重试次数
        en_US: Enter max retries
