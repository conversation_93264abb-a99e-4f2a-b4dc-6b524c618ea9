from collections.abc import Generator
from typing import Optional, Union
import sseclient
import json
from yarl import URL

from core.model_runtime.entities.llm_entities import LLMMode, LLMResult
from core.model_runtime.entities.message_entities import (
    PromptMessage,
    PromptMessageTool,
)
from core.model_runtime.model_providers.openai_api_compatible.llm.llm import OAIAPICompatLargeLanguageModel
from core.model_runtime.model_providers.openai.llm.llm import OpenAILargeLanguageModel
from core.model_runtime.model_providers.__base.large_language_model import *
import monitor
import logging
from core.model_runtime.entities.model_entities import (
    AIModelEntity,
    FetchFrom,
    ModelPropertyKey,
    ModelType,
    ParameterRule,
    ParameterType,
)
from core.model_runtime.entities.common_entities import I18nObject
from core.model_runtime.entities.llm_entities import LLMMode, LLMResult, LLMResultChunk, LLMResultChunkDelta
from flask import request, make_response, current_app,g
from flask.testing import FlaskClient


def mock_streaming_response() -> Generator:
    for i in range(10):
        yield LLMResultChunk(
            model="",
            prompt_messages=[],
            delta=LLMResultChunkDelta(
                index=i, message=AssistantPromptMessage(content=f"msg({i})", tool_cals=[]), finish_reason="stop" if i == 9 else None, usage=LLMUsage.empty_usage()),
        )


class KagentLargeLanguageModel(OAIAPICompatLargeLanguageModel):
    def _invoke(
        self,
        model: str,
        credentials: dict,
        prompt_messages: list[PromptMessage],
        model_parameters: dict,
        tools: Optional[list[PromptMessageTool]] = None,
        stop: Optional[list[str]] = None,
        stream: bool = True,
        user: Optional[str] = None,
    ) -> Union[LLMResult, Generator]:
        logging.info(f"[kris][kagent] kagent-llm model invoke")
        logging.info(f"[kris][kagent] prompt_messages:{prompt_messages}")
        logging.info(f"[kris][kagent] model_parameters:{model_parameters}")
        logging.info(f"[kris][kagent] stream:{stream}")
        logging.info(f"[kris][kagent] credentials:{credentials}")

        uin= ""
        rtx= ""
        debug_info = "mock_llm_as_agent"
        try:
            if hasattr(g, 'extend_info') and g.extend_info is not None:
                uin = g.extend_info.get("uin", "")
                rtx = g.extend_info.get("rtx", "")
                debug_info = g.extend_info.get(
                    "debug_info", "mock_llm_as_agent")
                logging.info(
                    f"[kris] uin:{uin} debug_info:{debug_info} rtx:{rtx}")
        except Exception as e:
            logger.error(f"[kris] get user_info fail {e}")

        # a94cb90d-48a0-4a87-8f9e-8baae2e55459
        agent_app_id = credentials['api_key']
        agent_params = json.loads(prompt_messages[0].content, strict=False)
        logging.info(
            f"[kris][kagent] app:{agent_app_id} params:'{agent_params}'")

        client: FlaskClient = current_app.test_client()
        client.set_cookie("debug_info", debug_info)
        client.set_cookie("uin", uin)
        client.set_cookie("rtx", rtx)
        inner_response = client.post(
            "/v1/chat-messages",
            data=json.dumps(dict({
                "inputs": "",
                "query": "",
                "response_mode": "streaming",
                "conversation_id": "",
                "user": debug_info,
                "files": [
                ]
            }, **agent_params), ensure_ascii=False),
            headers={
                "Authorization": f"Bearer appid-{agent_app_id}",
                "content-type": "application/json",
            },
            query_string="",
            # stream=True,
        )

        logging.info(f"[kris][kagent] innser_resp: '{inner_response}'")
        # sse_client = sseclient.SSEClient(inner_response)
        # idx=0
        # for event in sse_client.events():
        #     logging.info(f"[kris][kagent] idx:{idx} Event: {event.event}, Data: {event.data}")
        #     idx+=1
        idx = 0
        for line in inner_response.response:
            try:
                l = json.loads(line.decode('utf-8')[6:].strip())
                logging.info(
                    f"[kris][kagent] {idx} Received chunk: #{l}# answer:#{l.get('answer', '')}#")
                idx += 1
                ans=l.get('answer', '')
                if len(ans)>0:
                    yield LLMResultChunk(
                        model="",
                        prompt_messages=[],
                        delta=LLMResultChunkDelta(
                            index=0, message=AssistantPromptMessage(content=ans, tool_cals=[]), finish_reason=None, usage=LLMUsage.empty_usage()),
                    )
            except Exception as e:
                logging.info(
                    f"[kris][kagent][error] {idx} Received chunk: #{line.decode('utf-8')}  # ")
                idx += 1
                
        yield LLMResultChunk(
            model="",
            prompt_messages=[],
            delta=LLMResultChunkDelta(
                index=0, message=AssistantPromptMessage(content="", tool_cals=[]), finish_reason='stop', usage=LLMUsage.empty_usage()),
        )
        # return mock_streaming_response()

    def validate_credentials(self, model: str, credentials: dict) -> None:
        # self._add_custom_parameters(credentials)
        # super().validate_credentials(model, credentials)
        # never here
        pass

    @staticmethod
    def _add_custom_parameters(credentials) -> None:
        credentials["endpoint_url"] = str(
            URL(credentials.get("endpoint_url", "https://api.deepseek.com")))
        credentials["mode"] = LLMMode.CHAT.value
        credentials["function_calling_type"] = "tool_call"
        credentials["stream_function_calling"] = "support"
        logging.info(f"[kris] called.credentials:'{credentials}'")

    def get_customizable_model_schema(self, model: str, credentials: dict) -> Optional[AIModelEntity]:
        """
        used to define customizable model schema
        """
        rules = [
            ParameterRule(
                name="temperature",
                type=ParameterType.FLOAT,
                use_template="temperature",
                label=I18nObject(zh_Hans="温度", en_US="Temperature"),
            ),
            ParameterRule(
                name="top_p",
                type=ParameterType.FLOAT,
                use_template="top_p",
                label=I18nObject(zh_Hans="Top P", en_US="Top P"),
            ),
            ParameterRule(
                name="top_k",
                type=ParameterType.INT,
                use_template="top_k",
                min=1,
                default=1,
                label=I18nObject(zh_Hans="Top K", en_US="Top K"),
            ),
            ParameterRule(
                name="max_tokens",
                type=ParameterType.INT,
                use_template="max_tokens",
                min=1,
                default=512,
                label=I18nObject(zh_Hans="最大生成长度", en_US="Max Tokens"),
            ),
        ]

        entity = AIModelEntity(
            model=model,
            label=I18nObject(en_US=model),
            fetch_from=FetchFrom.CUSTOMIZABLE_MODEL,
            model_type=ModelType.LLM,
            model_properties={
                ModelPropertyKey.MODE: LLMMode.COMPLETION.value,
            },
            parameter_rules=rules,
        )

        return entity
