import logging

from core.model_runtime.entities.model_entities import ModelType
from core.model_runtime.errors.validate import CredentialsValidateFailedError
from core.model_runtime.model_providers.__base.model_provider import ModelProvider

logger = logging.getLogger(__name__)


class KagentProvider(ModelProvider):
    def validate_provider_credentials(self, credentials: dict) -> None:
        try:
            model_instance = self.get_model_instance(ModelType.LLM)
            credentials["openai_api_key"] = credentials["api_key"]
            credentials["openai_api_base"] = credentials["endpoint_url"]
            credentials["real_model_name"] = "fake"
            model = "fake_llm_by_dify_agent"
            logger.info(f"[kris] credentials:'{credentials}'")
            # model_instance.validate_credentials(model=model, credentials=credentials)
        except CredentialsValidateFailedError as ex:
            raise ex
        except Exception as ex:
            logger.exception(
                f"{self.get_provider_schema().provider} credentials validate failed")
            raise ex
