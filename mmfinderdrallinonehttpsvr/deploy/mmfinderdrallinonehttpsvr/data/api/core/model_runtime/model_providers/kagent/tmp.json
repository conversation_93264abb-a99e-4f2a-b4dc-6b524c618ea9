{"data": [{"provider": "openai_api_compatible", "label": {"zh_Hans": "OpenAI-API-compatible", "en_US": "OpenAI-API-compatible"}, "description": {"zh_Hans": "兼容 OpenAI API 的模型供应商，例如 LM Studio 。", "en_US": "Model providers compatible with OpenAI's API standard, such as LM Studio."}, "icon_small": null, "icon_large": null, "background": null, "help": null, "supported_model_types": ["llm", "text-embedding", "speech2text", "rerank", "tts"], "configurate_methods": ["customizable-model"], "provider_credential_schema": null, "model_credential_schema": {"model": {"label": {"zh_Hans": "模型名称", "en_US": "Model Name"}, "placeholder": {"zh_Hans": "输入模型全称", "en_US": "Enter full model name"}}, "credential_form_schemas": [{"variable": "api_key", "label": {"zh_Hans": "API Key(useless)", "en_US": "API Key(useless)"}, "type": "text-input", "required": false, "default": null, "options": null, "placeholder": {"zh_Hans": "在此输入您的 API Key", "en_US": "Enter your API Key"}, "max_length": 0, "show_on": []}, {"variable": "endpoint_url", "label": {"zh_Hans": "内网接口url", "en_US": "API endpoint URL"}, "type": "text-input", "required": true, "default": null, "options": null, "placeholder": {"zh_Hans": "Base URL, e.g. https://api.openai.com/v1", "en_US": "Base URL, e.g. https://api.openai.com/v1"}, "max_length": 0, "show_on": []}, {"variable": "mode", "label": {"zh_Hans": "Completion mode", "en_US": "Completion mode"}, "type": "select", "required": false, "default": "chat", "options": [{"label": {"zh_Hans": "补全", "en_US": "Completion"}, "value": "completion", "show_on": []}, {"label": {"zh_Hans": "对话", "en_US": "Cha<PERSON>"}, "value": "chat", "show_on": []}], "placeholder": {"zh_Hans": "选择对话类型", "en_US": "Select completion mode"}, "max_length": 0, "show_on": [{"variable": "__model_type", "value": "llm"}]}, {"variable": "context_size", "label": {"zh_Hans": "模型上下文长度", "en_US": "Model context size"}, "type": "text-input", "required": true, "default": "4096", "options": null, "placeholder": {"zh_Hans": "在此输入您的模型上下文长度", "en_US": "Enter your Model context size"}, "max_length": 0, "show_on": [{"variable": "__model_type", "value": "llm"}]}, {"variable": "context_size", "label": {"zh_Hans": "模型上下文长度", "en_US": "Model context size"}, "type": "text-input", "required": true, "default": "4096", "options": null, "placeholder": {"zh_Hans": "在此输入您的模型上下文长度", "en_US": "Enter your Model context size"}, "max_length": 0, "show_on": [{"variable": "__model_type", "value": "text-embedding"}]}, {"variable": "context_size", "label": {"zh_Hans": "模型上下文长度", "en_US": "Model context size"}, "type": "text-input", "required": true, "default": "4096", "options": null, "placeholder": {"zh_Hans": "在此输入您的模型上下文长度", "en_US": "Enter your Model context size"}, "max_length": 0, "show_on": [{"variable": "__model_type", "value": "rerank"}]}, {"variable": "max_tokens_to_sample", "label": {"zh_Hans": "最大 token 上限", "en_US": "Upper bound for max tokens"}, "type": "text-input", "required": true, "default": "4096", "options": null, "placeholder": null, "max_length": 0, "show_on": [{"variable": "__model_type", "value": "llm"}]}, {"variable": "function_calling_type", "label": {"zh_Hans": "Function calling", "en_US": "Function calling"}, "type": "select", "required": false, "default": "no_call", "options": [{"label": {"zh_Hans": "Function Call", "en_US": "Function Call"}, "value": "function_call", "show_on": []}, {"label": {"zh_Hans": "Tool Call", "en_US": "Tool Call"}, "value": "tool_call", "show_on": []}, {"label": {"zh_Hans": "不支持", "en_US": "Not Support"}, "value": "no_call", "show_on": []}], "placeholder": null, "max_length": 0, "show_on": [{"variable": "__model_type", "value": "llm"}]}, {"variable": "stream_function_calling", "label": {"zh_Hans": "Stream function calling", "en_US": "Stream function calling"}, "type": "select", "required": false, "default": "not_supported", "options": [{"label": {"zh_Hans": "支持", "en_US": "Support"}, "value": "supported", "show_on": []}, {"label": {"zh_Hans": "不支持", "en_US": "Not Support"}, "value": "not_supported", "show_on": []}], "placeholder": null, "max_length": 0, "show_on": [{"variable": "__model_type", "value": "llm"}]}, {"variable": "vision_support", "label": {"zh_Hans": "Vision 支持", "en_US": "Vision Support"}, "type": "select", "required": false, "default": "no_support", "options": [{"label": {"zh_Hans": "支持", "en_US": "Support"}, "value": "support", "show_on": []}, {"label": {"zh_Hans": "不支持", "en_US": "Not Support"}, "value": "no_support", "show_on": []}], "placeholder": null, "max_length": 0, "show_on": [{"variable": "__model_type", "value": "llm"}]}, {"variable": "stream_mode_delimiter", "label": {"zh_Hans": "流模式返回结果的分隔符", "en_US": "Delimiter for streaming results"}, "type": "text-input", "required": true, "default": "\n\n", "options": null, "placeholder": null, "max_length": 0, "show_on": [{"variable": "__model_type", "value": "llm"}]}, {"variable": "voices", "label": {"zh_Hans": "可用声音（用英文逗号分隔）", "en_US": "Available Voices (comma-separated)"}, "type": "text-input", "required": false, "default": "alloy", "options": null, "placeholder": {"zh_Hans": "alloy,echo,fable,onyx,nova,shimmer", "en_US": "alloy,echo,fable,onyx,nova,shimmer"}, "max_length": 0, "show_on": [{"variable": "__model_type", "value": "tts"}]}]}, "preferred_provider_type": "custom", "custom_configuration": {"status": "active"}, "system_configuration": {"enabled": false, "current_quota_type": null, "quota_configurations": []}}, {"provider": "ollama", "label": {"zh_Hans": "Ollama", "en_US": "Ollama"}, "description": null, "icon_small": {"zh_Hans": "/console/api/workspaces/current/model-providers/ollama/icon_small/zh_Hans", "en_US": "/console/api/workspaces/current/model-providers/ollama/icon_small/en_US"}, "icon_large": {"zh_Hans": "/console/api/workspaces/current/model-providers/ollama/icon_large/zh_Hans", "en_US": "/console/api/workspaces/current/model-providers/ollama/icon_large/en_US"}, "background": "#F9FAFB", "help": {"title": {"zh_Hans": "如何集成 O<PERSON>ma", "en_US": "How to integrate with Ollama"}, "url": {"zh_Hans": "https://docs.dify.ai/tutorials/model-configuration/ollama", "en_US": "https://docs.dify.ai/tutorials/model-configuration/ollama"}}, "supported_model_types": ["llm", "text-embedding"], "configurate_methods": ["customizable-model"], "provider_credential_schema": null, "model_credential_schema": {"model": {"label": {"zh_Hans": "模型名称", "en_US": "Model Name"}, "placeholder": {"zh_Hans": "输入模型名称", "en_US": "Enter your model name"}}, "credential_form_schemas": [{"variable": "base_url", "label": {"zh_Hans": "基础 URL", "en_US": "Base URL"}, "type": "text-input", "required": true, "default": null, "options": null, "placeholder": {"zh_Hans": "Ollama server 的基础 URL，例如 http://mmfinderdrassistantsvr.prod.polaris:29929", "en_US": "Base url of Ollama server, e.g. http://mmfinderdrassistantsvr.prod.polaris:29929"}, "max_length": 0, "show_on": []}, {"variable": "mode", "label": {"zh_Hans": "模型类型", "en_US": "Completion mode"}, "type": "select", "required": true, "default": "chat", "options": [{"label": {"zh_Hans": "补全", "en_US": "Completion"}, "value": "completion", "show_on": []}, {"label": {"zh_Hans": "对话", "en_US": "Cha<PERSON>"}, "value": "chat", "show_on": []}], "placeholder": {"zh_Hans": "选择对话类型", "en_US": "Select completion mode"}, "max_length": 0, "show_on": [{"variable": "__model_type", "value": "llm"}]}, {"variable": "context_size", "label": {"zh_Hans": "模型上下文长度", "en_US": "Model context size"}, "type": "text-input", "required": true, "default": "4096", "options": null, "placeholder": {"zh_Hans": "在此输入您的模型上下文长度", "en_US": "Enter your Model context size"}, "max_length": 0, "show_on": []}, {"variable": "max_tokens", "label": {"zh_Hans": "最大 token 上限", "en_US": "Upper bound for max tokens"}, "type": "text-input", "required": true, "default": "4096", "options": null, "placeholder": null, "max_length": 0, "show_on": [{"variable": "__model_type", "value": "llm"}]}, {"variable": "vision_support", "label": {"zh_Hans": "是否支持 Vision", "en_US": "Vision support"}, "type": "radio", "required": false, "default": "false", "options": [{"label": {"zh_Hans": "是", "en_US": "Yes"}, "value": "true", "show_on": []}, {"label": {"zh_Hans": "否", "en_US": "No"}, "value": "false", "show_on": []}], "placeholder": null, "max_length": 0, "show_on": [{"variable": "__model_type", "value": "llm"}]}, {"variable": "function_call_support", "label": {"zh_Hans": "是否支持函数调用", "en_US": "Function call support"}, "type": "radio", "required": false, "default": "false", "options": [{"label": {"zh_Hans": "是", "en_US": "Yes"}, "value": "true", "show_on": []}, {"label": {"zh_Hans": "否", "en_US": "No"}, "value": "false", "show_on": []}], "placeholder": null, "max_length": 0, "show_on": [{"variable": "__model_type", "value": "llm"}]}]}, "preferred_provider_type": "custom", "custom_configuration": {"status": "active"}, "system_configuration": {"enabled": false, "current_quota_type": null, "quota_configurations": []}}, {"provider": "xinference", "label": {"zh_Hans": "Xorbits Inference", "en_US": "Xorbits Inference"}, "description": null, "icon_small": {"zh_Hans": "/console/api/workspaces/current/model-providers/xinference/icon_small/zh_Hans", "en_US": "/console/api/workspaces/current/model-providers/xinference/icon_small/en_US"}, "icon_large": {"zh_Hans": "/console/api/workspaces/current/model-providers/xinference/icon_large/zh_Hans", "en_US": "/console/api/workspaces/current/model-providers/xinference/icon_large/en_US"}, "background": "#FAF5FF", "help": {"title": {"zh_Hans": "如何部署 Xinference", "en_US": "How to deploy Xinference"}, "url": {"zh_Hans": "https://github.com/xorbitsai/inference", "en_US": "https://github.com/xorbitsai/inference"}}, "supported_model_types": ["llm", "text-embedding", "rerank", "speech2text", "tts"], "configurate_methods": ["customizable-model"], "provider_credential_schema": null, "model_credential_schema": {"model": {"label": {"zh_Hans": "模型名称", "en_US": "Model Name"}, "placeholder": {"zh_Hans": "输入模型名称", "en_US": "Enter your model name"}}, "credential_form_schemas": [{"variable": "server_url", "label": {"zh_Hans": "服务器URL", "en_US": "Server url"}, "type": "text-input", "required": true, "default": null, "options": null, "placeholder": {"zh_Hans": "在此输入Xinference的服务器地址，如 http://192.168.1.100:9997", "en_US": "Enter the url of your Xinference, e.g. http://192.168.1.100:9997"}, "max_length": 0, "show_on": []}, {"variable": "model_uid", "label": {"zh_Hans": "模型UID", "en_US": "Model uid"}, "type": "text-input", "required": true, "default": null, "options": null, "placeholder": {"zh_Hans": "在此输入您的Model UID", "en_US": "Enter the model uid"}, "max_length": 0, "show_on": []}, {"variable": "api_key", "label": {"zh_Hans": "API密钥", "en_US": "API key"}, "type": "text-input", "required": false, "default": null, "options": null, "placeholder": {"zh_Hans": "在此输入您的API密钥", "en_US": "Enter the api key"}, "max_length": 0, "show_on": []}, {"variable": "invoke_timeout", "label": {"zh_Hans": "调用超时时间 (单位:秒)", "en_US": "invoke timeout (unit:second)"}, "type": "text-input", "required": true, "default": "60", "options": null, "placeholder": {"zh_Hans": "在此输入调用超时时间", "en_US": "Enter invoke timeout value"}, "max_length": 0, "show_on": []}, {"variable": "max_retries", "label": {"zh_Hans": "调用重试次数", "en_US": "max retries"}, "type": "text-input", "required": true, "default": "3", "options": null, "placeholder": {"zh_Hans": "在此输入调用重试次数", "en_US": "Enter max retries"}, "max_length": 0, "show_on": []}]}, "preferred_provider_type": "custom", "custom_configuration": {"status": "active"}, "system_configuration": {"enabled": false, "current_quota_type": null, "quota_configurations": []}}, {"provider": "deepseek", "label": {"zh_Hans": "深度求索", "en_US": "deepseek"}, "description": {"zh_Hans": "深度求索提供的模型，例如 deepseek-chat、deepseek-coder 。", "en_US": "Models provided by deepseek, such as deepseek-chat、deepseek-coder."}, "icon_small": {"zh_Hans": "/console/api/workspaces/current/model-providers/deepseek/icon_small/zh_Hans", "en_US": "/console/api/workspaces/current/model-providers/deepseek/icon_small/en_US"}, "icon_large": {"zh_Hans": "/console/api/workspaces/current/model-providers/deepseek/icon_large/zh_Hans", "en_US": "/console/api/workspaces/current/model-providers/deepseek/icon_large/en_US"}, "background": "#c0cdff", "help": {"title": {"zh_Hans": "从深度求索获取 API Key", "en_US": "Get your API Key from deepseek"}, "url": {"zh_Hans": "https://platform.deepseek.com/api_keys", "en_US": "https://platform.deepseek.com/api_keys"}}, "supported_model_types": ["llm"], "configurate_methods": ["predefined-model"], "provider_credential_schema": {"credential_form_schemas": [{"variable": "api_key", "label": {"zh_Hans": "API Key", "en_US": "API Key"}, "type": "text-input", "required": true, "default": null, "options": null, "placeholder": {"zh_Hans": "在此输入您的 API Key", "en_US": "Enter your API Key"}, "max_length": 0, "show_on": []}, {"variable": "endpoint_url", "label": {"zh_Hans": "自定义 API endpoint 地址", "en_US": "Custom API endpoint URL"}, "type": "text-input", "required": false, "default": null, "options": null, "placeholder": {"zh_Hans": "Base URL, e.g. https://api.deepseek.com/v1 or https://api.deepseek.com", "en_US": "Base URL, e.g. https://api.deepseek.com/v1 or https://api.deepseek.com"}, "max_length": 0, "show_on": []}]}, "model_credential_schema": null, "preferred_provider_type": "custom", "custom_configuration": {"status": "active"}, "system_configuration": {"enabled": false, "current_quota_type": null, "quota_configurations": []}}, {"provider": "ka<PERSON>", "label": {"zh_Hans": "长得像大模型的agent(流式)", "en_US": "ka<PERSON>"}, "description": {"zh_Hans": "长得像大模型的agent(流式)", "en_US": "call agent like llm"}, "icon_small": {"zh_Hans": "/console/api/workspaces/current/model-providers/kagent/icon_small/zh_Hans", "en_US": "/console/api/workspaces/current/model-providers/kagent/icon_small/en_US"}, "icon_large": {"zh_Hans": "/console/api/workspaces/current/model-providers/kagent/icon_large/zh_Hans", "en_US": "/console/api/workspaces/current/model-providers/kagent/icon_large/en_US"}, "background": "#c0cdff", "help": {"title": {"zh_Hans": "随便写无所谓", "en_US": "appid-xxx-xxx"}, "url": {"zh_Hans": "https://platform.kagent.com/api_keys", "en_US": "https://platform.kagent.com/api_keys"}}, "supported_model_types": ["llm"], "configurate_methods": ["customizable-model"], "provider_credential_schema": {"credential_form_schemas": [{"variable": "api_key", "label": {"zh_Hans": "API Key", "en_US": "API Key"}, "type": "text-input", "required": true, "default": null, "options": null, "placeholder": {"zh_Hans": "在此输入您的 API Key", "en_US": "Enter your API Key"}, "max_length": 0, "show_on": []}, {"variable": "endpoint_url", "label": {"zh_Hans": "自定义 API endpoint 地址", "en_US": "Custom API endpoint URL"}, "type": "text-input", "required": false, "default": null, "options": null, "placeholder": {"zh_Hans": "Base URL, e.g. https://api.kagent.com/v1 or https://api.kagent.com", "en_US": "Base URL, e.g. https://api.kagent.com/v1 or https://api.kagent.com"}, "max_length": 0, "show_on": []}]}, "model_credential_schema": null, "preferred_provider_type": "custom", "custom_configuration": {"status": "no-configure"}, "system_configuration": {"enabled": false, "current_quota_type": null, "quota_configurations": []}}, {"provider": "openai", "label": {"zh_Hans": "OpenAI", "en_US": "OpenAI"}, "description": {"zh_Hans": "OpenAI 提供的模型，例如 GPT-3.5-Turbo 和 GPT-4。", "en_US": "Models provided by OpenAI, such as GPT-3.5-Turbo and GPT-4."}, "icon_small": {"zh_Hans": "/console/api/workspaces/current/model-providers/openai/icon_small/zh_Hans", "en_US": "/console/api/workspaces/current/model-providers/openai/icon_small/en_US"}, "icon_large": {"zh_Hans": "/console/api/workspaces/current/model-providers/openai/icon_large/zh_Hans", "en_US": "/console/api/workspaces/current/model-providers/openai/icon_large/en_US"}, "background": "#E5E7EB", "help": {"title": {"zh_Hans": "从 OpenAI 获取 API Key", "en_US": "Get your API Key from OpenAI"}, "url": {"zh_Hans": "https://platform.openai.com/account/api-keys", "en_US": "https://platform.openai.com/account/api-keys"}}, "supported_model_types": ["llm", "text-embedding", "speech2text", "moderation", "tts"], "configurate_methods": ["predefined-model", "customizable-model"], "provider_credential_schema": {"credential_form_schemas": [{"variable": "openai_api_key", "label": {"zh_Hans": "API Key", "en_US": "API Key"}, "type": "text-input", "required": true, "default": null, "options": null, "placeholder": {"zh_Hans": "在此输入您的 API Key", "en_US": "Enter your API Key"}, "max_length": 0, "show_on": []}, {"variable": "openai_organization", "label": {"zh_Hans": "组织 ID", "en_US": "Organization"}, "type": "text-input", "required": false, "default": null, "options": null, "placeholder": {"zh_Hans": "在此输入您的组织 ID", "en_US": "Enter your Organization ID"}, "max_length": 0, "show_on": []}, {"variable": "openai_api_base", "label": {"zh_Hans": "API Base", "en_US": "API Base"}, "type": "text-input", "required": false, "default": null, "options": null, "placeholder": {"zh_Hans": "在此输入您的 API Base, 如：https://api.openai.com", "en_US": "Enter your API Base, e.g. https://api.openai.com"}, "max_length": 0, "show_on": []}]}, "model_credential_schema": {"model": {"label": {"zh_Hans": "模型名称", "en_US": "Model Name"}, "placeholder": {"zh_Hans": "输入模型名称", "en_US": "Enter your model name"}}, "credential_form_schemas": [{"variable": "openai_api_key", "label": {"zh_Hans": "API Key", "en_US": "API Key"}, "type": "text-input", "required": true, "default": null, "options": null, "placeholder": {"zh_Hans": "在此输入您的 API Key", "en_US": "Enter your API Key"}, "max_length": 0, "show_on": []}, {"variable": "openai_organization", "label": {"zh_Hans": "组织 ID", "en_US": "Organization"}, "type": "text-input", "required": false, "default": null, "options": null, "placeholder": {"zh_Hans": "在此输入您的组织 ID", "en_US": "Enter your Organization ID"}, "max_length": 0, "show_on": []}, {"variable": "openai_api_base", "label": {"zh_Hans": "API Base", "en_US": "API Base"}, "type": "text-input", "required": false, "default": null, "options": null, "placeholder": {"zh_Hans": "在此输入您的 API Base", "en_US": "Enter your API Base"}, "max_length": 0, "show_on": []}]}, "preferred_provider_type": "custom", "custom_configuration": {"status": "no-configure"}, "system_configuration": {"enabled": false, "current_quota_type": null, "quota_configurations": []}}, {"provider": "openllm", "label": {"zh_Hans": "OpenLLM", "en_US": "OpenLLM"}, "description": null, "icon_small": {"zh_Hans": "/console/api/workspaces/current/model-providers/openllm/icon_small/zh_Hans", "en_US": "/console/api/workspaces/current/model-providers/openllm/icon_small/en_US"}, "icon_large": {"zh_Hans": "/console/api/workspaces/current/model-providers/openllm/icon_large/zh_Hans", "en_US": "/console/api/workspaces/current/model-providers/openllm/icon_large/en_US"}, "background": "#F9FAFB", "help": {"title": {"zh_Hans": "如何部署 OpenLLM", "en_US": "How to deploy OpenLLM"}, "url": {"zh_Hans": "https://github.com/bentoml/OpenLLM", "en_US": "https://github.com/bentoml/OpenLLM"}}, "supported_model_types": ["llm", "text-embedding"], "configurate_methods": ["customizable-model"], "provider_credential_schema": null, "model_credential_schema": {"model": {"label": {"zh_Hans": "模型名称", "en_US": "Model Name"}, "placeholder": {"zh_Hans": "输入模型名称", "en_US": "Enter your model name"}}, "credential_form_schemas": [{"variable": "server_url", "label": {"zh_Hans": "服务器URL", "en_US": "Server url"}, "type": "text-input", "required": true, "default": null, "options": null, "placeholder": {"zh_Hans": "在此输入OpenLLM的服务器地址，如 http://192.168.1.100:3000", "en_US": "Enter the url of your OpenLLM, e.g. http://192.168.1.100:3000"}, "max_length": 0, "show_on": []}]}, "preferred_provider_type": "custom", "custom_configuration": {"status": "no-configure"}, "system_configuration": {"enabled": false, "current_quota_type": null, "quota_configurations": []}}]}