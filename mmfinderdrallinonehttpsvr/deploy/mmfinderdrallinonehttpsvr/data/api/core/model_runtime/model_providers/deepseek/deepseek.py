import logging

from core.model_runtime.entities.model_entities import ModelType
from core.model_runtime.errors.validate import CredentialsValidateFailedError
from core.model_runtime.model_providers.__base.model_provider import ModelProvider

logger = logging.getLogger(__name__)


class DeepSeekProvider(ModelProvider):
    def validate_provider_credentials(self, credentials: dict) -> None:
        """
        Validate provider credentials
        if validate failed, raise exception

        :param credentials: provider credentials, credentials form defined in `provider_credential_schema`.
        """
        try:
            model_instance = self.get_model_instance(ModelType.LLM)

            credentials["openai_api_key"] = credentials["api_key"]
            credentials["openai_api_base"] = credentials["endpoint_url"]
            credentials["real_model_name"] = "DeepSeek-V3"
            model = "deepseek-chat"

            # Use `deepseek-chat` model for validate,
            # no matter what model you pass in, text completion model or chat model
            model_instance.validate_credentials(model=model, credentials=credentials)
        except CredentialsValidateFailedError as ex:
            raise ex
        except Exception as ex:
            logger.exception(f"{self.get_provider_schema().provider} credentials validate failed")
            raise ex
