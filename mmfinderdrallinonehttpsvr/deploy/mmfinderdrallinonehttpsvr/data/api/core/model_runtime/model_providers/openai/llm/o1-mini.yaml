model: o1-mini
label:
  zh_Hans: o1-mini
  en_US: o1-mini
model_type: llm
features:
  - agent-thought
model_properties:
  mode: chat
  context_size: 128000
parameter_rules:
  - name: max_tokens
    use_template: max_tokens
    default: 65536
    min: 1
    max: 65536
  - name: response_format
    label:
      zh_Hans: 回复格式
      en_US: response_format
    type: string
    help:
      zh_Hans: 指定模型必须输出的格式
      en_US: specifying the format that the model must output
    required: false
    options:
      - text
      - json_object
pricing:
  input: '3.00'
  output: '12.00'
  unit: '0.000001'
  currency: USD
