// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: mmbiz/mmbizocr/svr/mmvisionocrparagraphdet/mmvisionocrparagraphdet.proto

#include "mmbiz/mmbizocr/svr/mmvisionocrparagraphdet/mmvisionocrparagraphdet.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)
namespace mmvisionocrparagraphdet {
class PingReqDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<PingReq>
      _instance;
} _PingReq_default_instance_;
class PingRespDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<PingResp>
      _instance;
} _PingResp_default_instance_;
class CVImgDataDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CVImgData>
      _instance;
} _CVImgData_default_instance_;
class PointDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Point>
      _instance;
} _Point_default_instance_;
class PolyDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Poly>
      _instance;
} _Poly_default_instance_;
class ParagraphDetReqDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ParagraphDetReq>
      _instance;
} _ParagraphDetReq_default_instance_;
class ParagraphDetRespDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ParagraphDetResp>
      _instance;
} _ParagraphDetResp_default_instance_;
class DocLayoutReqDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<DocLayoutReq>
      _instance;
} _DocLayoutReq_default_instance_;
class DocLayoutRespDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<DocLayoutResp>
      _instance;
} _DocLayoutResp_default_instance_;
}  // namespace mmvisionocrparagraphdet
namespace protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto {
void InitDefaultsPingReqImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::mmvisionocrparagraphdet::_PingReq_default_instance_;
    new (ptr) ::mmvisionocrparagraphdet::PingReq();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::mmvisionocrparagraphdet::PingReq::InitAsDefaultInstance();
}

void InitDefaultsPingReq() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsPingReqImpl);
}

void InitDefaultsPingRespImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::mmvisionocrparagraphdet::_PingResp_default_instance_;
    new (ptr) ::mmvisionocrparagraphdet::PingResp();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::mmvisionocrparagraphdet::PingResp::InitAsDefaultInstance();
}

void InitDefaultsPingResp() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsPingRespImpl);
}

void InitDefaultsCVImgDataImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::mmvisionocrparagraphdet::_CVImgData_default_instance_;
    new (ptr) ::mmvisionocrparagraphdet::CVImgData();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::mmvisionocrparagraphdet::CVImgData::InitAsDefaultInstance();
}

void InitDefaultsCVImgData() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsCVImgDataImpl);
}

void InitDefaultsPointImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::mmvisionocrparagraphdet::_Point_default_instance_;
    new (ptr) ::mmvisionocrparagraphdet::Point();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::mmvisionocrparagraphdet::Point::InitAsDefaultInstance();
}

void InitDefaultsPoint() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsPointImpl);
}

void InitDefaultsPolyImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::InitDefaultsPoint();
  {
    void* ptr = &::mmvisionocrparagraphdet::_Poly_default_instance_;
    new (ptr) ::mmvisionocrparagraphdet::Poly();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::mmvisionocrparagraphdet::Poly::InitAsDefaultInstance();
}

void InitDefaultsPoly() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsPolyImpl);
}

void InitDefaultsParagraphDetReqImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::InitDefaultsCVImgData();
  {
    void* ptr = &::mmvisionocrparagraphdet::_ParagraphDetReq_default_instance_;
    new (ptr) ::mmvisionocrparagraphdet::ParagraphDetReq();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::mmvisionocrparagraphdet::ParagraphDetReq::InitAsDefaultInstance();
}

void InitDefaultsParagraphDetReq() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsParagraphDetReqImpl);
}

void InitDefaultsParagraphDetRespImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::InitDefaultsPoly();
  {
    void* ptr = &::mmvisionocrparagraphdet::_ParagraphDetResp_default_instance_;
    new (ptr) ::mmvisionocrparagraphdet::ParagraphDetResp();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::mmvisionocrparagraphdet::ParagraphDetResp::InitAsDefaultInstance();
}

void InitDefaultsParagraphDetResp() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsParagraphDetRespImpl);
}

void InitDefaultsDocLayoutReqImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::mmvisionocrparagraphdet::_DocLayoutReq_default_instance_;
    new (ptr) ::mmvisionocrparagraphdet::DocLayoutReq();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::mmvisionocrparagraphdet::DocLayoutReq::InitAsDefaultInstance();
}

void InitDefaultsDocLayoutReq() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsDocLayoutReqImpl);
}

void InitDefaultsDocLayoutRespImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::mmvisionocrparagraphdet::_DocLayoutResp_default_instance_;
    new (ptr) ::mmvisionocrparagraphdet::DocLayoutResp();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::mmvisionocrparagraphdet::DocLayoutResp::InitAsDefaultInstance();
}

void InitDefaultsDocLayoutResp() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsDocLayoutRespImpl);
}

::google::protobuf::Metadata file_level_metadata[9];
const ::google::protobuf::ServiceDescriptor* file_level_service_descriptors[1];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::PingReq, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::PingReq, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::PingReq, useruin_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::PingReq, str_),
  1,
  0,
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::PingResp, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::PingResp, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::PingResp, str_),
  0,
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::CVImgData, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::CVImgData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::CVImgData, rows_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::CVImgData, cols_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::CVImgData, type_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::CVImgData, data_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::CVImgData, step_),
  1,
  2,
  3,
  0,
  4,
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::Point, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::Point, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::Point, x_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::Point, y_),
  0,
  1,
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::Poly, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::Poly, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::Poly, points_),
  ~0u,
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::ParagraphDetReq, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::ParagraphDetReq, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::ParagraphDetReq, cv_img_data_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::ParagraphDetReq, img_data_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::ParagraphDetReq, source_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::ParagraphDetReq, useruin_),
  1,
  0,
  2,
  3,
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::ParagraphDetResp, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::ParagraphDetResp, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::ParagraphDetResp, para_boxes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::ParagraphDetResp, scores_),
  ~0u,
  ~0u,
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::DocLayoutReq, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::DocLayoutReq, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::DocLayoutReq, pdf_data_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::DocLayoutReq, img_data_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::DocLayoutReq, source_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::DocLayoutReq, useruin_),
  0,
  1,
  2,
  3,
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::DocLayoutResp, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::DocLayoutResp, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::DocLayoutResp, word_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::DocLayoutResp, content_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::DocLayoutResp, b64_data_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::mmvisionocrparagraphdet::DocLayoutResp, title_),
  0,
  1,
  2,
  3,
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, 7, sizeof(::mmvisionocrparagraphdet::PingReq)},
  { 9, 15, sizeof(::mmvisionocrparagraphdet::PingResp)},
  { 16, 26, sizeof(::mmvisionocrparagraphdet::CVImgData)},
  { 31, 38, sizeof(::mmvisionocrparagraphdet::Point)},
  { 40, 46, sizeof(::mmvisionocrparagraphdet::Poly)},
  { 47, 56, sizeof(::mmvisionocrparagraphdet::ParagraphDetReq)},
  { 60, 67, sizeof(::mmvisionocrparagraphdet::ParagraphDetResp)},
  { 69, 78, sizeof(::mmvisionocrparagraphdet::DocLayoutReq)},
  { 82, 91, sizeof(::mmvisionocrparagraphdet::DocLayoutResp)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::mmvisionocrparagraphdet::_PingReq_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::mmvisionocrparagraphdet::_PingResp_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::mmvisionocrparagraphdet::_CVImgData_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::mmvisionocrparagraphdet::_Point_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::mmvisionocrparagraphdet::_Poly_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::mmvisionocrparagraphdet::_ParagraphDetReq_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::mmvisionocrparagraphdet::_ParagraphDetResp_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::mmvisionocrparagraphdet::_DocLayoutReq_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::mmvisionocrparagraphdet::_DocLayoutResp_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  ::google::protobuf::MessageFactory* factory = NULL;
  AssignDescriptors(
      "mmbiz/mmbizocr/svr/mmvisionocrparagraphdet/mmvisionocrparagraphdet.proto", schemas, file_default_instances, TableStruct::offsets, factory,
      file_level_metadata, NULL, file_level_service_descriptors);
}

void protobuf_AssignDescriptorsOnce() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 9);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\nHmmbiz/mmbizocr/svr/mmvisionocrparagrap"
      "hdet/mmvisionocrparagraphdet.proto\022\027mmvi"
      "sionocrparagraphdet\032#comm2/tlvpickle/skb"
      "uiltintype.proto\"\'\n\007PingReq\022\017\n\007useruin\030\001"
      " \001(\r\022\013\n\003str\030\002 \001(\t\"\027\n\010PingResp\022\013\n\003str\030\001 \001"
      "(\t\"Q\n\tCVImgData\022\014\n\004rows\030\001 \001(\005\022\014\n\004cols\030\002 "
      "\001(\005\022\014\n\004type\030\003 \001(\005\022\014\n\004data\030\004 \001(\014\022\014\n\004step\030"
      "\005 \001(\r\"\035\n\005Point\022\t\n\001x\030\001 \001(\001\022\t\n\001y\030\002 \001(\001\"6\n\004"
      "Poly\022.\n\006points\030\001 \003(\0132\036.mmvisionocrparagr"
      "aphdet.Point\"}\n\017ParagraphDetReq\0227\n\013cv_im"
      "g_data\030\001 \001(\0132\".mmvisionocrparagraphdet.C"
      "VImgData\022\020\n\010img_data\030\002 \001(\014\022\016\n\006source\030\003 \001"
      "(\r\022\017\n\007useruin\030\004 \001(\r\"U\n\020ParagraphDetResp\022"
      "1\n\npara_boxes\030\001 \003(\0132\035.mmvisionocrparagra"
      "phdet.Poly\022\016\n\006scores\030\002 \003(\002\"V\n\014DocLayoutR"
      "eq\022\020\n\010pdf_data\030\001 \001(\014\022\020\n\010img_data\030\002 \001(\014\022\021"
      "\n\006source\030\003 \001(\r:\0010\022\017\n\007useruin\030\004 \001(\r\"U\n\rDo"
      "cLayoutResp\022\022\n\nword_bytes\030\001 \001(\014\022\017\n\007conte"
      "nt\030\002 \001(\t\022\020\n\010b64_data\030\003 \001(\t\022\r\n\005title\030\004 \001("
      "\t2\263\003\n\027MMVisionOcrParagraphDet\022r\n\004Ping\022 ."
      "mmvisionocrparagraphdet.PingReq\032!.mmvisi"
      "onocrparagraphdet.PingResp\"%\200\244\350\003\001\212\244\350\003\004u:"
      "s:\222\244\350\003\022-u <uin> -s <str> \022\217\001\n\014GetParaBox"
      "es\022(.mmvisionocrparagraphdet.ParagraphDe"
      "tReq\032).mmvisionocrparagraphdet.Paragraph"
      "DetResp\"*\200\244\350\003\002\212\244\350\003\004u:q:\222\244\350\003\027-u <useruin>"
      " -q <query>\022\211\001\n\014GetDocLayout\022%.mmvisiono"
      "crparagraphdet.DocLayoutReq\032&.mmvisionoc"
      "rparagraphdet.DocLayoutResp\"*\200\244\350\003\003\212\244\350\003\004u"
      ":q:\222\244\350\003\027-u <useruin> -q <query>\032\006\210\244\350\003\261PB"
      "\003\220\001\001"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 1204);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "mmbiz/mmbizocr/svr/mmvisionocrparagraphdet/mmvisionocrparagraphdet.proto", &protobuf_RegisterTypes);
  ::protobuf_comm2_2ftlvpickle_2fskbuiltintype_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto
namespace mmvisionocrparagraphdet {

// ===================================================================

void PingReq::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int PingReq::kUseruinFieldNumber;
const int PingReq::kStrFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

PingReq::PingReq()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::InitDefaultsPingReq();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:mmvisionocrparagraphdet.PingReq)
}
PingReq::PingReq(const PingReq& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _has_bits_(from._has_bits_),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  str_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.has_str()) {
    str_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.str_);
  }
  useruin_ = from.useruin_;
  // @@protoc_insertion_point(copy_constructor:mmvisionocrparagraphdet.PingReq)
}

void PingReq::SharedCtor() {
  _cached_size_ = 0;
  str_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  useruin_ = 0u;
}

PingReq::~PingReq() {
  // @@protoc_insertion_point(destructor:mmvisionocrparagraphdet.PingReq)
  SharedDtor();
}

void PingReq::SharedDtor() {
  str_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void PingReq::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* PingReq::descriptor() {
  ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const PingReq& PingReq::default_instance() {
  ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::InitDefaultsPingReq();
  return *internal_default_instance();
}

PingReq* PingReq::New(::google::protobuf::Arena* arena) const {
  PingReq* n = new PingReq;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void PingReq::Clear() {
// @@protoc_insertion_point(message_clear_start:mmvisionocrparagraphdet.PingReq)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    GOOGLE_DCHECK(!str_.IsDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited()));
    (*str_.UnsafeRawStringPointer())->clear();
  }
  useruin_ = 0u;
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

bool PingReq::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:mmvisionocrparagraphdet.PingReq)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional uint32 useruin = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          set_has_useruin();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &useruin_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // optional string str = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_str()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->str().data(), static_cast<int>(this->str().length()),
            ::google::protobuf::internal::WireFormat::PARSE,
            "mmvisionocrparagraphdet.PingReq.str");
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:mmvisionocrparagraphdet.PingReq)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:mmvisionocrparagraphdet.PingReq)
  return false;
#undef DO_
}

void PingReq::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:mmvisionocrparagraphdet.PingReq)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional uint32 useruin = 1;
  if (cached_has_bits & 0x00000002u) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->useruin(), output);
  }

  // optional string str = 2;
  if (cached_has_bits & 0x00000001u) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->str().data(), static_cast<int>(this->str().length()),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "mmvisionocrparagraphdet.PingReq.str");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->str(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:mmvisionocrparagraphdet.PingReq)
}

::google::protobuf::uint8* PingReq::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:mmvisionocrparagraphdet.PingReq)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional uint32 useruin = 1;
  if (cached_has_bits & 0x00000002u) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->useruin(), target);
  }

  // optional string str = 2;
  if (cached_has_bits & 0x00000001u) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->str().data(), static_cast<int>(this->str().length()),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "mmvisionocrparagraphdet.PingReq.str");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->str(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:mmvisionocrparagraphdet.PingReq)
  return target;
}

size_t PingReq::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:mmvisionocrparagraphdet.PingReq)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  if (_has_bits_[0 / 32] & 3u) {
    // optional string str = 2;
    if (has_str()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->str());
    }

    // optional uint32 useruin = 1;
    if (has_useruin()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->useruin());
    }

  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void PingReq::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:mmvisionocrparagraphdet.PingReq)
  GOOGLE_DCHECK_NE(&from, this);
  const PingReq* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const PingReq>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:mmvisionocrparagraphdet.PingReq)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:mmvisionocrparagraphdet.PingReq)
    MergeFrom(*source);
  }
}

void PingReq::MergeFrom(const PingReq& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:mmvisionocrparagraphdet.PingReq)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 3u) {
    if (cached_has_bits & 0x00000001u) {
      set_has_str();
      str_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.str_);
    }
    if (cached_has_bits & 0x00000002u) {
      useruin_ = from.useruin_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void PingReq::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:mmvisionocrparagraphdet.PingReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void PingReq::CopyFrom(const PingReq& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:mmvisionocrparagraphdet.PingReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PingReq::IsInitialized() const {
  return true;
}

void PingReq::Swap(PingReq* other) {
  if (other == this) return;
  InternalSwap(other);
}
void PingReq::InternalSwap(PingReq* other) {
  using std::swap;
  str_.Swap(&other->str_);
  swap(useruin_, other->useruin_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata PingReq::GetMetadata() const {
  protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void PingResp::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int PingResp::kStrFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

PingResp::PingResp()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::InitDefaultsPingResp();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:mmvisionocrparagraphdet.PingResp)
}
PingResp::PingResp(const PingResp& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _has_bits_(from._has_bits_),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  str_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.has_str()) {
    str_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.str_);
  }
  // @@protoc_insertion_point(copy_constructor:mmvisionocrparagraphdet.PingResp)
}

void PingResp::SharedCtor() {
  _cached_size_ = 0;
  str_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

PingResp::~PingResp() {
  // @@protoc_insertion_point(destructor:mmvisionocrparagraphdet.PingResp)
  SharedDtor();
}

void PingResp::SharedDtor() {
  str_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void PingResp::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* PingResp::descriptor() {
  ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const PingResp& PingResp::default_instance() {
  ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::InitDefaultsPingResp();
  return *internal_default_instance();
}

PingResp* PingResp::New(::google::protobuf::Arena* arena) const {
  PingResp* n = new PingResp;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void PingResp::Clear() {
// @@protoc_insertion_point(message_clear_start:mmvisionocrparagraphdet.PingResp)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    GOOGLE_DCHECK(!str_.IsDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited()));
    (*str_.UnsafeRawStringPointer())->clear();
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

bool PingResp::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:mmvisionocrparagraphdet.PingResp)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string str = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_str()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->str().data(), static_cast<int>(this->str().length()),
            ::google::protobuf::internal::WireFormat::PARSE,
            "mmvisionocrparagraphdet.PingResp.str");
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:mmvisionocrparagraphdet.PingResp)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:mmvisionocrparagraphdet.PingResp)
  return false;
#undef DO_
}

void PingResp::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:mmvisionocrparagraphdet.PingResp)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional string str = 1;
  if (cached_has_bits & 0x00000001u) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->str().data(), static_cast<int>(this->str().length()),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "mmvisionocrparagraphdet.PingResp.str");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->str(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:mmvisionocrparagraphdet.PingResp)
}

::google::protobuf::uint8* PingResp::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:mmvisionocrparagraphdet.PingResp)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional string str = 1;
  if (cached_has_bits & 0x00000001u) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->str().data(), static_cast<int>(this->str().length()),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "mmvisionocrparagraphdet.PingResp.str");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->str(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:mmvisionocrparagraphdet.PingResp)
  return target;
}

size_t PingResp::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:mmvisionocrparagraphdet.PingResp)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  // optional string str = 1;
  if (has_str()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->str());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void PingResp::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:mmvisionocrparagraphdet.PingResp)
  GOOGLE_DCHECK_NE(&from, this);
  const PingResp* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const PingResp>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:mmvisionocrparagraphdet.PingResp)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:mmvisionocrparagraphdet.PingResp)
    MergeFrom(*source);
  }
}

void PingResp::MergeFrom(const PingResp& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:mmvisionocrparagraphdet.PingResp)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_str()) {
    set_has_str();
    str_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.str_);
  }
}

void PingResp::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:mmvisionocrparagraphdet.PingResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void PingResp::CopyFrom(const PingResp& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:mmvisionocrparagraphdet.PingResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PingResp::IsInitialized() const {
  return true;
}

void PingResp::Swap(PingResp* other) {
  if (other == this) return;
  InternalSwap(other);
}
void PingResp::InternalSwap(PingResp* other) {
  using std::swap;
  str_.Swap(&other->str_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata PingResp::GetMetadata() const {
  protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void CVImgData::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CVImgData::kRowsFieldNumber;
const int CVImgData::kColsFieldNumber;
const int CVImgData::kTypeFieldNumber;
const int CVImgData::kDataFieldNumber;
const int CVImgData::kStepFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CVImgData::CVImgData()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::InitDefaultsCVImgData();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:mmvisionocrparagraphdet.CVImgData)
}
CVImgData::CVImgData(const CVImgData& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _has_bits_(from._has_bits_),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.has_data()) {
    data_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.data_);
  }
  ::memcpy(&rows_, &from.rows_,
    static_cast<size_t>(reinterpret_cast<char*>(&step_) -
    reinterpret_cast<char*>(&rows_)) + sizeof(step_));
  // @@protoc_insertion_point(copy_constructor:mmvisionocrparagraphdet.CVImgData)
}

void CVImgData::SharedCtor() {
  _cached_size_ = 0;
  data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&rows_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&step_) -
      reinterpret_cast<char*>(&rows_)) + sizeof(step_));
}

CVImgData::~CVImgData() {
  // @@protoc_insertion_point(destructor:mmvisionocrparagraphdet.CVImgData)
  SharedDtor();
}

void CVImgData::SharedDtor() {
  data_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void CVImgData::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* CVImgData::descriptor() {
  ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const CVImgData& CVImgData::default_instance() {
  ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::InitDefaultsCVImgData();
  return *internal_default_instance();
}

CVImgData* CVImgData::New(::google::protobuf::Arena* arena) const {
  CVImgData* n = new CVImgData;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void CVImgData::Clear() {
// @@protoc_insertion_point(message_clear_start:mmvisionocrparagraphdet.CVImgData)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    GOOGLE_DCHECK(!data_.IsDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited()));
    (*data_.UnsafeRawStringPointer())->clear();
  }
  if (cached_has_bits & 30u) {
    ::memset(&rows_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&step_) -
        reinterpret_cast<char*>(&rows_)) + sizeof(step_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

bool CVImgData::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:mmvisionocrparagraphdet.CVImgData)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 rows = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          set_has_rows();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &rows_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // optional int32 cols = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {
          set_has_cols();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &cols_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // optional int32 type = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {
          set_has_type();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &type_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // optional bytes data = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_data()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // optional uint32 step = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {
          set_has_step();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &step_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:mmvisionocrparagraphdet.CVImgData)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:mmvisionocrparagraphdet.CVImgData)
  return false;
#undef DO_
}

void CVImgData::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:mmvisionocrparagraphdet.CVImgData)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional int32 rows = 1;
  if (cached_has_bits & 0x00000002u) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->rows(), output);
  }

  // optional int32 cols = 2;
  if (cached_has_bits & 0x00000004u) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->cols(), output);
  }

  // optional int32 type = 3;
  if (cached_has_bits & 0x00000008u) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->type(), output);
  }

  // optional bytes data = 4;
  if (cached_has_bits & 0x00000001u) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      4, this->data(), output);
  }

  // optional uint32 step = 5;
  if (cached_has_bits & 0x00000010u) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(5, this->step(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:mmvisionocrparagraphdet.CVImgData)
}

::google::protobuf::uint8* CVImgData::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:mmvisionocrparagraphdet.CVImgData)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional int32 rows = 1;
  if (cached_has_bits & 0x00000002u) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->rows(), target);
  }

  // optional int32 cols = 2;
  if (cached_has_bits & 0x00000004u) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->cols(), target);
  }

  // optional int32 type = 3;
  if (cached_has_bits & 0x00000008u) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->type(), target);
  }

  // optional bytes data = 4;
  if (cached_has_bits & 0x00000001u) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        4, this->data(), target);
  }

  // optional uint32 step = 5;
  if (cached_has_bits & 0x00000010u) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(5, this->step(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:mmvisionocrparagraphdet.CVImgData)
  return target;
}

size_t CVImgData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:mmvisionocrparagraphdet.CVImgData)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  if (_has_bits_[0 / 32] & 31u) {
    // optional bytes data = 4;
    if (has_data()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->data());
    }

    // optional int32 rows = 1;
    if (has_rows()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->rows());
    }

    // optional int32 cols = 2;
    if (has_cols()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->cols());
    }

    // optional int32 type = 3;
    if (has_type()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->type());
    }

    // optional uint32 step = 5;
    if (has_step()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->step());
    }

  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void CVImgData::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:mmvisionocrparagraphdet.CVImgData)
  GOOGLE_DCHECK_NE(&from, this);
  const CVImgData* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CVImgData>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:mmvisionocrparagraphdet.CVImgData)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:mmvisionocrparagraphdet.CVImgData)
    MergeFrom(*source);
  }
}

void CVImgData::MergeFrom(const CVImgData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:mmvisionocrparagraphdet.CVImgData)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 31u) {
    if (cached_has_bits & 0x00000001u) {
      set_has_data();
      data_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.data_);
    }
    if (cached_has_bits & 0x00000002u) {
      rows_ = from.rows_;
    }
    if (cached_has_bits & 0x00000004u) {
      cols_ = from.cols_;
    }
    if (cached_has_bits & 0x00000008u) {
      type_ = from.type_;
    }
    if (cached_has_bits & 0x00000010u) {
      step_ = from.step_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void CVImgData::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:mmvisionocrparagraphdet.CVImgData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CVImgData::CopyFrom(const CVImgData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:mmvisionocrparagraphdet.CVImgData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CVImgData::IsInitialized() const {
  return true;
}

void CVImgData::Swap(CVImgData* other) {
  if (other == this) return;
  InternalSwap(other);
}
void CVImgData::InternalSwap(CVImgData* other) {
  using std::swap;
  data_.Swap(&other->data_);
  swap(rows_, other->rows_);
  swap(cols_, other->cols_);
  swap(type_, other->type_);
  swap(step_, other->step_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata CVImgData::GetMetadata() const {
  protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Point::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Point::kXFieldNumber;
const int Point::kYFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Point::Point()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::InitDefaultsPoint();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:mmvisionocrparagraphdet.Point)
}
Point::Point(const Point& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _has_bits_(from._has_bits_),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&x_, &from.x_,
    static_cast<size_t>(reinterpret_cast<char*>(&y_) -
    reinterpret_cast<char*>(&x_)) + sizeof(y_));
  // @@protoc_insertion_point(copy_constructor:mmvisionocrparagraphdet.Point)
}

void Point::SharedCtor() {
  _cached_size_ = 0;
  ::memset(&x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&y_) -
      reinterpret_cast<char*>(&x_)) + sizeof(y_));
}

Point::~Point() {
  // @@protoc_insertion_point(destructor:mmvisionocrparagraphdet.Point)
  SharedDtor();
}

void Point::SharedDtor() {
}

void Point::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Point::descriptor() {
  ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Point& Point::default_instance() {
  ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::InitDefaultsPoint();
  return *internal_default_instance();
}

Point* Point::New(::google::protobuf::Arena* arena) const {
  Point* n = new Point;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Point::Clear() {
// @@protoc_insertion_point(message_clear_start:mmvisionocrparagraphdet.Point)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 3u) {
    ::memset(&x_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&y_) -
        reinterpret_cast<char*>(&x_)) + sizeof(y_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

bool Point::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:mmvisionocrparagraphdet.Point)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional double x = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(9u /* 9 & 0xFF */)) {
          set_has_x();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &x_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // optional double y = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(17u /* 17 & 0xFF */)) {
          set_has_y();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &y_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:mmvisionocrparagraphdet.Point)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:mmvisionocrparagraphdet.Point)
  return false;
#undef DO_
}

void Point::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:mmvisionocrparagraphdet.Point)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional double x = 1;
  if (cached_has_bits & 0x00000001u) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(1, this->x(), output);
  }

  // optional double y = 2;
  if (cached_has_bits & 0x00000002u) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(2, this->y(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:mmvisionocrparagraphdet.Point)
}

::google::protobuf::uint8* Point::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:mmvisionocrparagraphdet.Point)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional double x = 1;
  if (cached_has_bits & 0x00000001u) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(1, this->x(), target);
  }

  // optional double y = 2;
  if (cached_has_bits & 0x00000002u) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(2, this->y(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:mmvisionocrparagraphdet.Point)
  return target;
}

size_t Point::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:mmvisionocrparagraphdet.Point)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  if (_has_bits_[0 / 32] & 3u) {
    // optional double x = 1;
    if (has_x()) {
      total_size += 1 + 8;
    }

    // optional double y = 2;
    if (has_y()) {
      total_size += 1 + 8;
    }

  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Point::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:mmvisionocrparagraphdet.Point)
  GOOGLE_DCHECK_NE(&from, this);
  const Point* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Point>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:mmvisionocrparagraphdet.Point)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:mmvisionocrparagraphdet.Point)
    MergeFrom(*source);
  }
}

void Point::MergeFrom(const Point& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:mmvisionocrparagraphdet.Point)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 3u) {
    if (cached_has_bits & 0x00000001u) {
      x_ = from.x_;
    }
    if (cached_has_bits & 0x00000002u) {
      y_ = from.y_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void Point::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:mmvisionocrparagraphdet.Point)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Point::CopyFrom(const Point& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:mmvisionocrparagraphdet.Point)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Point::IsInitialized() const {
  return true;
}

void Point::Swap(Point* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Point::InternalSwap(Point* other) {
  using std::swap;
  swap(x_, other->x_);
  swap(y_, other->y_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Point::GetMetadata() const {
  protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Poly::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Poly::kPointsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Poly::Poly()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::InitDefaultsPoly();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:mmvisionocrparagraphdet.Poly)
}
Poly::Poly(const Poly& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _has_bits_(from._has_bits_),
      _cached_size_(0),
      points_(from.points_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:mmvisionocrparagraphdet.Poly)
}

void Poly::SharedCtor() {
  _cached_size_ = 0;
}

Poly::~Poly() {
  // @@protoc_insertion_point(destructor:mmvisionocrparagraphdet.Poly)
  SharedDtor();
}

void Poly::SharedDtor() {
}

void Poly::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Poly::descriptor() {
  ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Poly& Poly::default_instance() {
  ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::InitDefaultsPoly();
  return *internal_default_instance();
}

Poly* Poly::New(::google::protobuf::Arena* arena) const {
  Poly* n = new Poly;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Poly::Clear() {
// @@protoc_insertion_point(message_clear_start:mmvisionocrparagraphdet.Poly)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  points_.Clear();
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

bool Poly::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:mmvisionocrparagraphdet.Poly)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .mmvisionocrparagraphdet.Point points = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(input, add_points()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:mmvisionocrparagraphdet.Poly)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:mmvisionocrparagraphdet.Poly)
  return false;
#undef DO_
}

void Poly::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:mmvisionocrparagraphdet.Poly)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .mmvisionocrparagraphdet.Point points = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->points_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->points(static_cast<int>(i)), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:mmvisionocrparagraphdet.Poly)
}

::google::protobuf::uint8* Poly::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:mmvisionocrparagraphdet.Poly)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .mmvisionocrparagraphdet.Point points = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->points_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->points(static_cast<int>(i)), deterministic, target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:mmvisionocrparagraphdet.Poly)
  return target;
}

size_t Poly::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:mmvisionocrparagraphdet.Poly)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  // repeated .mmvisionocrparagraphdet.Point points = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->points_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->points(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Poly::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:mmvisionocrparagraphdet.Poly)
  GOOGLE_DCHECK_NE(&from, this);
  const Poly* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Poly>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:mmvisionocrparagraphdet.Poly)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:mmvisionocrparagraphdet.Poly)
    MergeFrom(*source);
  }
}

void Poly::MergeFrom(const Poly& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:mmvisionocrparagraphdet.Poly)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  points_.MergeFrom(from.points_);
}

void Poly::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:mmvisionocrparagraphdet.Poly)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Poly::CopyFrom(const Poly& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:mmvisionocrparagraphdet.Poly)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Poly::IsInitialized() const {
  return true;
}

void Poly::Swap(Poly* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Poly::InternalSwap(Poly* other) {
  using std::swap;
  points_.InternalSwap(&other->points_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Poly::GetMetadata() const {
  protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ParagraphDetReq::InitAsDefaultInstance() {
  ::mmvisionocrparagraphdet::_ParagraphDetReq_default_instance_._instance.get_mutable()->cv_img_data_ = const_cast< ::mmvisionocrparagraphdet::CVImgData*>(
      ::mmvisionocrparagraphdet::CVImgData::internal_default_instance());
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ParagraphDetReq::kCvImgDataFieldNumber;
const int ParagraphDetReq::kImgDataFieldNumber;
const int ParagraphDetReq::kSourceFieldNumber;
const int ParagraphDetReq::kUseruinFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ParagraphDetReq::ParagraphDetReq()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::InitDefaultsParagraphDetReq();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:mmvisionocrparagraphdet.ParagraphDetReq)
}
ParagraphDetReq::ParagraphDetReq(const ParagraphDetReq& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _has_bits_(from._has_bits_),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  img_data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.has_img_data()) {
    img_data_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.img_data_);
  }
  if (from.has_cv_img_data()) {
    cv_img_data_ = new ::mmvisionocrparagraphdet::CVImgData(*from.cv_img_data_);
  } else {
    cv_img_data_ = NULL;
  }
  ::memcpy(&source_, &from.source_,
    static_cast<size_t>(reinterpret_cast<char*>(&useruin_) -
    reinterpret_cast<char*>(&source_)) + sizeof(useruin_));
  // @@protoc_insertion_point(copy_constructor:mmvisionocrparagraphdet.ParagraphDetReq)
}

void ParagraphDetReq::SharedCtor() {
  _cached_size_ = 0;
  img_data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&cv_img_data_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&useruin_) -
      reinterpret_cast<char*>(&cv_img_data_)) + sizeof(useruin_));
}

ParagraphDetReq::~ParagraphDetReq() {
  // @@protoc_insertion_point(destructor:mmvisionocrparagraphdet.ParagraphDetReq)
  SharedDtor();
}

void ParagraphDetReq::SharedDtor() {
  img_data_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete cv_img_data_;
}

void ParagraphDetReq::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ParagraphDetReq::descriptor() {
  ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ParagraphDetReq& ParagraphDetReq::default_instance() {
  ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::InitDefaultsParagraphDetReq();
  return *internal_default_instance();
}

ParagraphDetReq* ParagraphDetReq::New(::google::protobuf::Arena* arena) const {
  ParagraphDetReq* n = new ParagraphDetReq;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ParagraphDetReq::Clear() {
// @@protoc_insertion_point(message_clear_start:mmvisionocrparagraphdet.ParagraphDetReq)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 3u) {
    if (cached_has_bits & 0x00000001u) {
      GOOGLE_DCHECK(!img_data_.IsDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited()));
      (*img_data_.UnsafeRawStringPointer())->clear();
    }
    if (cached_has_bits & 0x00000002u) {
      GOOGLE_DCHECK(cv_img_data_ != NULL);
      cv_img_data_->Clear();
    }
  }
  if (cached_has_bits & 12u) {
    ::memset(&source_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&useruin_) -
        reinterpret_cast<char*>(&source_)) + sizeof(useruin_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

bool ParagraphDetReq::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:mmvisionocrparagraphdet.ParagraphDetReq)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .mmvisionocrparagraphdet.CVImgData cv_img_data = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_cv_img_data()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // optional bytes img_data = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_img_data()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // optional uint32 source = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {
          set_has_source();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &source_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // optional uint32 useruin = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {
          set_has_useruin();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &useruin_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:mmvisionocrparagraphdet.ParagraphDetReq)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:mmvisionocrparagraphdet.ParagraphDetReq)
  return false;
#undef DO_
}

void ParagraphDetReq::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:mmvisionocrparagraphdet.ParagraphDetReq)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional .mmvisionocrparagraphdet.CVImgData cv_img_data = 1;
  if (cached_has_bits & 0x00000002u) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->cv_img_data_, output);
  }

  // optional bytes img_data = 2;
  if (cached_has_bits & 0x00000001u) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      2, this->img_data(), output);
  }

  // optional uint32 source = 3;
  if (cached_has_bits & 0x00000004u) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(3, this->source(), output);
  }

  // optional uint32 useruin = 4;
  if (cached_has_bits & 0x00000008u) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(4, this->useruin(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:mmvisionocrparagraphdet.ParagraphDetReq)
}

::google::protobuf::uint8* ParagraphDetReq::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:mmvisionocrparagraphdet.ParagraphDetReq)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional .mmvisionocrparagraphdet.CVImgData cv_img_data = 1;
  if (cached_has_bits & 0x00000002u) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, *this->cv_img_data_, deterministic, target);
  }

  // optional bytes img_data = 2;
  if (cached_has_bits & 0x00000001u) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        2, this->img_data(), target);
  }

  // optional uint32 source = 3;
  if (cached_has_bits & 0x00000004u) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(3, this->source(), target);
  }

  // optional uint32 useruin = 4;
  if (cached_has_bits & 0x00000008u) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(4, this->useruin(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:mmvisionocrparagraphdet.ParagraphDetReq)
  return target;
}

size_t ParagraphDetReq::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:mmvisionocrparagraphdet.ParagraphDetReq)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  if (_has_bits_[0 / 32] & 15u) {
    // optional bytes img_data = 2;
    if (has_img_data()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->img_data());
    }

    // optional .mmvisionocrparagraphdet.CVImgData cv_img_data = 1;
    if (has_cv_img_data()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *this->cv_img_data_);
    }

    // optional uint32 source = 3;
    if (has_source()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->source());
    }

    // optional uint32 useruin = 4;
    if (has_useruin()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->useruin());
    }

  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ParagraphDetReq::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:mmvisionocrparagraphdet.ParagraphDetReq)
  GOOGLE_DCHECK_NE(&from, this);
  const ParagraphDetReq* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ParagraphDetReq>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:mmvisionocrparagraphdet.ParagraphDetReq)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:mmvisionocrparagraphdet.ParagraphDetReq)
    MergeFrom(*source);
  }
}

void ParagraphDetReq::MergeFrom(const ParagraphDetReq& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:mmvisionocrparagraphdet.ParagraphDetReq)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 15u) {
    if (cached_has_bits & 0x00000001u) {
      set_has_img_data();
      img_data_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.img_data_);
    }
    if (cached_has_bits & 0x00000002u) {
      mutable_cv_img_data()->::mmvisionocrparagraphdet::CVImgData::MergeFrom(from.cv_img_data());
    }
    if (cached_has_bits & 0x00000004u) {
      source_ = from.source_;
    }
    if (cached_has_bits & 0x00000008u) {
      useruin_ = from.useruin_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void ParagraphDetReq::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:mmvisionocrparagraphdet.ParagraphDetReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ParagraphDetReq::CopyFrom(const ParagraphDetReq& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:mmvisionocrparagraphdet.ParagraphDetReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ParagraphDetReq::IsInitialized() const {
  return true;
}

void ParagraphDetReq::Swap(ParagraphDetReq* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ParagraphDetReq::InternalSwap(ParagraphDetReq* other) {
  using std::swap;
  img_data_.Swap(&other->img_data_);
  swap(cv_img_data_, other->cv_img_data_);
  swap(source_, other->source_);
  swap(useruin_, other->useruin_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ParagraphDetReq::GetMetadata() const {
  protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ParagraphDetResp::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ParagraphDetResp::kParaBoxesFieldNumber;
const int ParagraphDetResp::kScoresFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ParagraphDetResp::ParagraphDetResp()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::InitDefaultsParagraphDetResp();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:mmvisionocrparagraphdet.ParagraphDetResp)
}
ParagraphDetResp::ParagraphDetResp(const ParagraphDetResp& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _has_bits_(from._has_bits_),
      _cached_size_(0),
      para_boxes_(from.para_boxes_),
      scores_(from.scores_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:mmvisionocrparagraphdet.ParagraphDetResp)
}

void ParagraphDetResp::SharedCtor() {
  _cached_size_ = 0;
}

ParagraphDetResp::~ParagraphDetResp() {
  // @@protoc_insertion_point(destructor:mmvisionocrparagraphdet.ParagraphDetResp)
  SharedDtor();
}

void ParagraphDetResp::SharedDtor() {
}

void ParagraphDetResp::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ParagraphDetResp::descriptor() {
  ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ParagraphDetResp& ParagraphDetResp::default_instance() {
  ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::InitDefaultsParagraphDetResp();
  return *internal_default_instance();
}

ParagraphDetResp* ParagraphDetResp::New(::google::protobuf::Arena* arena) const {
  ParagraphDetResp* n = new ParagraphDetResp;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ParagraphDetResp::Clear() {
// @@protoc_insertion_point(message_clear_start:mmvisionocrparagraphdet.ParagraphDetResp)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  para_boxes_.Clear();
  scores_.Clear();
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

bool ParagraphDetResp::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:mmvisionocrparagraphdet.ParagraphDetResp)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .mmvisionocrparagraphdet.Poly para_boxes = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(input, add_para_boxes()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated float scores = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(21u /* 21 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 1, 21u, input, this->mutable_scores())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, this->mutable_scores())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:mmvisionocrparagraphdet.ParagraphDetResp)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:mmvisionocrparagraphdet.ParagraphDetResp)
  return false;
#undef DO_
}

void ParagraphDetResp::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:mmvisionocrparagraphdet.ParagraphDetResp)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .mmvisionocrparagraphdet.Poly para_boxes = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->para_boxes_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->para_boxes(static_cast<int>(i)), output);
  }

  // repeated float scores = 2;
  for (int i = 0, n = this->scores_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(
      2, this->scores(i), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:mmvisionocrparagraphdet.ParagraphDetResp)
}

::google::protobuf::uint8* ParagraphDetResp::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:mmvisionocrparagraphdet.ParagraphDetResp)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .mmvisionocrparagraphdet.Poly para_boxes = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->para_boxes_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->para_boxes(static_cast<int>(i)), deterministic, target);
  }

  // repeated float scores = 2;
  target = ::google::protobuf::internal::WireFormatLite::
    WriteFloatToArray(2, this->scores_, target);

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:mmvisionocrparagraphdet.ParagraphDetResp)
  return target;
}

size_t ParagraphDetResp::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:mmvisionocrparagraphdet.ParagraphDetResp)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  // repeated .mmvisionocrparagraphdet.Poly para_boxes = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->para_boxes_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->para_boxes(static_cast<int>(i)));
    }
  }

  // repeated float scores = 2;
  {
    unsigned int count = static_cast<unsigned int>(this->scores_size());
    size_t data_size = 4UL * count;
    total_size += 1 *
                  ::google::protobuf::internal::FromIntSize(this->scores_size());
    total_size += data_size;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ParagraphDetResp::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:mmvisionocrparagraphdet.ParagraphDetResp)
  GOOGLE_DCHECK_NE(&from, this);
  const ParagraphDetResp* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ParagraphDetResp>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:mmvisionocrparagraphdet.ParagraphDetResp)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:mmvisionocrparagraphdet.ParagraphDetResp)
    MergeFrom(*source);
  }
}

void ParagraphDetResp::MergeFrom(const ParagraphDetResp& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:mmvisionocrparagraphdet.ParagraphDetResp)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  para_boxes_.MergeFrom(from.para_boxes_);
  scores_.MergeFrom(from.scores_);
}

void ParagraphDetResp::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:mmvisionocrparagraphdet.ParagraphDetResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ParagraphDetResp::CopyFrom(const ParagraphDetResp& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:mmvisionocrparagraphdet.ParagraphDetResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ParagraphDetResp::IsInitialized() const {
  return true;
}

void ParagraphDetResp::Swap(ParagraphDetResp* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ParagraphDetResp::InternalSwap(ParagraphDetResp* other) {
  using std::swap;
  para_boxes_.InternalSwap(&other->para_boxes_);
  scores_.InternalSwap(&other->scores_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ParagraphDetResp::GetMetadata() const {
  protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void DocLayoutReq::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int DocLayoutReq::kPdfDataFieldNumber;
const int DocLayoutReq::kImgDataFieldNumber;
const int DocLayoutReq::kSourceFieldNumber;
const int DocLayoutReq::kUseruinFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

DocLayoutReq::DocLayoutReq()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::InitDefaultsDocLayoutReq();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:mmvisionocrparagraphdet.DocLayoutReq)
}
DocLayoutReq::DocLayoutReq(const DocLayoutReq& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _has_bits_(from._has_bits_),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  pdf_data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.has_pdf_data()) {
    pdf_data_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.pdf_data_);
  }
  img_data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.has_img_data()) {
    img_data_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.img_data_);
  }
  ::memcpy(&source_, &from.source_,
    static_cast<size_t>(reinterpret_cast<char*>(&useruin_) -
    reinterpret_cast<char*>(&source_)) + sizeof(useruin_));
  // @@protoc_insertion_point(copy_constructor:mmvisionocrparagraphdet.DocLayoutReq)
}

void DocLayoutReq::SharedCtor() {
  _cached_size_ = 0;
  pdf_data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  img_data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&source_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&useruin_) -
      reinterpret_cast<char*>(&source_)) + sizeof(useruin_));
}

DocLayoutReq::~DocLayoutReq() {
  // @@protoc_insertion_point(destructor:mmvisionocrparagraphdet.DocLayoutReq)
  SharedDtor();
}

void DocLayoutReq::SharedDtor() {
  pdf_data_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  img_data_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void DocLayoutReq::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* DocLayoutReq::descriptor() {
  ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const DocLayoutReq& DocLayoutReq::default_instance() {
  ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::InitDefaultsDocLayoutReq();
  return *internal_default_instance();
}

DocLayoutReq* DocLayoutReq::New(::google::protobuf::Arena* arena) const {
  DocLayoutReq* n = new DocLayoutReq;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void DocLayoutReq::Clear() {
// @@protoc_insertion_point(message_clear_start:mmvisionocrparagraphdet.DocLayoutReq)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 3u) {
    if (cached_has_bits & 0x00000001u) {
      GOOGLE_DCHECK(!pdf_data_.IsDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited()));
      (*pdf_data_.UnsafeRawStringPointer())->clear();
    }
    if (cached_has_bits & 0x00000002u) {
      GOOGLE_DCHECK(!img_data_.IsDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited()));
      (*img_data_.UnsafeRawStringPointer())->clear();
    }
  }
  if (cached_has_bits & 12u) {
    ::memset(&source_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&useruin_) -
        reinterpret_cast<char*>(&source_)) + sizeof(useruin_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

bool DocLayoutReq::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:mmvisionocrparagraphdet.DocLayoutReq)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional bytes pdf_data = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_pdf_data()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // optional bytes img_data = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_img_data()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // optional uint32 source = 3 [default = 0];
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {
          set_has_source();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &source_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // optional uint32 useruin = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {
          set_has_useruin();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &useruin_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:mmvisionocrparagraphdet.DocLayoutReq)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:mmvisionocrparagraphdet.DocLayoutReq)
  return false;
#undef DO_
}

void DocLayoutReq::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:mmvisionocrparagraphdet.DocLayoutReq)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional bytes pdf_data = 1;
  if (cached_has_bits & 0x00000001u) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      1, this->pdf_data(), output);
  }

  // optional bytes img_data = 2;
  if (cached_has_bits & 0x00000002u) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      2, this->img_data(), output);
  }

  // optional uint32 source = 3 [default = 0];
  if (cached_has_bits & 0x00000004u) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(3, this->source(), output);
  }

  // optional uint32 useruin = 4;
  if (cached_has_bits & 0x00000008u) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(4, this->useruin(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:mmvisionocrparagraphdet.DocLayoutReq)
}

::google::protobuf::uint8* DocLayoutReq::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:mmvisionocrparagraphdet.DocLayoutReq)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional bytes pdf_data = 1;
  if (cached_has_bits & 0x00000001u) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        1, this->pdf_data(), target);
  }

  // optional bytes img_data = 2;
  if (cached_has_bits & 0x00000002u) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        2, this->img_data(), target);
  }

  // optional uint32 source = 3 [default = 0];
  if (cached_has_bits & 0x00000004u) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(3, this->source(), target);
  }

  // optional uint32 useruin = 4;
  if (cached_has_bits & 0x00000008u) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(4, this->useruin(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:mmvisionocrparagraphdet.DocLayoutReq)
  return target;
}

size_t DocLayoutReq::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:mmvisionocrparagraphdet.DocLayoutReq)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  if (_has_bits_[0 / 32] & 15u) {
    // optional bytes pdf_data = 1;
    if (has_pdf_data()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->pdf_data());
    }

    // optional bytes img_data = 2;
    if (has_img_data()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->img_data());
    }

    // optional uint32 source = 3 [default = 0];
    if (has_source()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->source());
    }

    // optional uint32 useruin = 4;
    if (has_useruin()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->useruin());
    }

  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void DocLayoutReq::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:mmvisionocrparagraphdet.DocLayoutReq)
  GOOGLE_DCHECK_NE(&from, this);
  const DocLayoutReq* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const DocLayoutReq>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:mmvisionocrparagraphdet.DocLayoutReq)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:mmvisionocrparagraphdet.DocLayoutReq)
    MergeFrom(*source);
  }
}

void DocLayoutReq::MergeFrom(const DocLayoutReq& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:mmvisionocrparagraphdet.DocLayoutReq)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 15u) {
    if (cached_has_bits & 0x00000001u) {
      set_has_pdf_data();
      pdf_data_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.pdf_data_);
    }
    if (cached_has_bits & 0x00000002u) {
      set_has_img_data();
      img_data_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.img_data_);
    }
    if (cached_has_bits & 0x00000004u) {
      source_ = from.source_;
    }
    if (cached_has_bits & 0x00000008u) {
      useruin_ = from.useruin_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void DocLayoutReq::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:mmvisionocrparagraphdet.DocLayoutReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DocLayoutReq::CopyFrom(const DocLayoutReq& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:mmvisionocrparagraphdet.DocLayoutReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DocLayoutReq::IsInitialized() const {
  return true;
}

void DocLayoutReq::Swap(DocLayoutReq* other) {
  if (other == this) return;
  InternalSwap(other);
}
void DocLayoutReq::InternalSwap(DocLayoutReq* other) {
  using std::swap;
  pdf_data_.Swap(&other->pdf_data_);
  img_data_.Swap(&other->img_data_);
  swap(source_, other->source_);
  swap(useruin_, other->useruin_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata DocLayoutReq::GetMetadata() const {
  protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void DocLayoutResp::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int DocLayoutResp::kWordBytesFieldNumber;
const int DocLayoutResp::kContentFieldNumber;
const int DocLayoutResp::kB64DataFieldNumber;
const int DocLayoutResp::kTitleFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

DocLayoutResp::DocLayoutResp()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::InitDefaultsDocLayoutResp();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:mmvisionocrparagraphdet.DocLayoutResp)
}
DocLayoutResp::DocLayoutResp(const DocLayoutResp& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _has_bits_(from._has_bits_),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  word_bytes_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.has_word_bytes()) {
    word_bytes_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.word_bytes_);
  }
  content_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.has_content()) {
    content_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.content_);
  }
  b64_data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.has_b64_data()) {
    b64_data_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.b64_data_);
  }
  title_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.has_title()) {
    title_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.title_);
  }
  // @@protoc_insertion_point(copy_constructor:mmvisionocrparagraphdet.DocLayoutResp)
}

void DocLayoutResp::SharedCtor() {
  _cached_size_ = 0;
  word_bytes_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  content_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  b64_data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  title_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

DocLayoutResp::~DocLayoutResp() {
  // @@protoc_insertion_point(destructor:mmvisionocrparagraphdet.DocLayoutResp)
  SharedDtor();
}

void DocLayoutResp::SharedDtor() {
  word_bytes_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  content_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  b64_data_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  title_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void DocLayoutResp::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* DocLayoutResp::descriptor() {
  ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const DocLayoutResp& DocLayoutResp::default_instance() {
  ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::InitDefaultsDocLayoutResp();
  return *internal_default_instance();
}

DocLayoutResp* DocLayoutResp::New(::google::protobuf::Arena* arena) const {
  DocLayoutResp* n = new DocLayoutResp;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void DocLayoutResp::Clear() {
// @@protoc_insertion_point(message_clear_start:mmvisionocrparagraphdet.DocLayoutResp)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 15u) {
    if (cached_has_bits & 0x00000001u) {
      GOOGLE_DCHECK(!word_bytes_.IsDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited()));
      (*word_bytes_.UnsafeRawStringPointer())->clear();
    }
    if (cached_has_bits & 0x00000002u) {
      GOOGLE_DCHECK(!content_.IsDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited()));
      (*content_.UnsafeRawStringPointer())->clear();
    }
    if (cached_has_bits & 0x00000004u) {
      GOOGLE_DCHECK(!b64_data_.IsDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited()));
      (*b64_data_.UnsafeRawStringPointer())->clear();
    }
    if (cached_has_bits & 0x00000008u) {
      GOOGLE_DCHECK(!title_.IsDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited()));
      (*title_.UnsafeRawStringPointer())->clear();
    }
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

bool DocLayoutResp::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:mmvisionocrparagraphdet.DocLayoutResp)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional bytes word_bytes = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_word_bytes()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // optional string content = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_content()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->content().data(), static_cast<int>(this->content().length()),
            ::google::protobuf::internal::WireFormat::PARSE,
            "mmvisionocrparagraphdet.DocLayoutResp.content");
        } else {
          goto handle_unusual;
        }
        break;
      }

      // optional string b64_data = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_b64_data()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->b64_data().data(), static_cast<int>(this->b64_data().length()),
            ::google::protobuf::internal::WireFormat::PARSE,
            "mmvisionocrparagraphdet.DocLayoutResp.b64_data");
        } else {
          goto handle_unusual;
        }
        break;
      }

      // optional string title = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_title()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->title().data(), static_cast<int>(this->title().length()),
            ::google::protobuf::internal::WireFormat::PARSE,
            "mmvisionocrparagraphdet.DocLayoutResp.title");
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:mmvisionocrparagraphdet.DocLayoutResp)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:mmvisionocrparagraphdet.DocLayoutResp)
  return false;
#undef DO_
}

void DocLayoutResp::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:mmvisionocrparagraphdet.DocLayoutResp)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional bytes word_bytes = 1;
  if (cached_has_bits & 0x00000001u) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      1, this->word_bytes(), output);
  }

  // optional string content = 2;
  if (cached_has_bits & 0x00000002u) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->content().data(), static_cast<int>(this->content().length()),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "mmvisionocrparagraphdet.DocLayoutResp.content");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->content(), output);
  }

  // optional string b64_data = 3;
  if (cached_has_bits & 0x00000004u) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->b64_data().data(), static_cast<int>(this->b64_data().length()),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "mmvisionocrparagraphdet.DocLayoutResp.b64_data");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->b64_data(), output);
  }

  // optional string title = 4;
  if (cached_has_bits & 0x00000008u) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->title().data(), static_cast<int>(this->title().length()),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "mmvisionocrparagraphdet.DocLayoutResp.title");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->title(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:mmvisionocrparagraphdet.DocLayoutResp)
}

::google::protobuf::uint8* DocLayoutResp::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:mmvisionocrparagraphdet.DocLayoutResp)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional bytes word_bytes = 1;
  if (cached_has_bits & 0x00000001u) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        1, this->word_bytes(), target);
  }

  // optional string content = 2;
  if (cached_has_bits & 0x00000002u) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->content().data(), static_cast<int>(this->content().length()),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "mmvisionocrparagraphdet.DocLayoutResp.content");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->content(), target);
  }

  // optional string b64_data = 3;
  if (cached_has_bits & 0x00000004u) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->b64_data().data(), static_cast<int>(this->b64_data().length()),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "mmvisionocrparagraphdet.DocLayoutResp.b64_data");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->b64_data(), target);
  }

  // optional string title = 4;
  if (cached_has_bits & 0x00000008u) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->title().data(), static_cast<int>(this->title().length()),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "mmvisionocrparagraphdet.DocLayoutResp.title");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->title(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:mmvisionocrparagraphdet.DocLayoutResp)
  return target;
}

size_t DocLayoutResp::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:mmvisionocrparagraphdet.DocLayoutResp)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  if (_has_bits_[0 / 32] & 15u) {
    // optional bytes word_bytes = 1;
    if (has_word_bytes()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->word_bytes());
    }

    // optional string content = 2;
    if (has_content()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->content());
    }

    // optional string b64_data = 3;
    if (has_b64_data()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->b64_data());
    }

    // optional string title = 4;
    if (has_title()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->title());
    }

  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void DocLayoutResp::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:mmvisionocrparagraphdet.DocLayoutResp)
  GOOGLE_DCHECK_NE(&from, this);
  const DocLayoutResp* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const DocLayoutResp>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:mmvisionocrparagraphdet.DocLayoutResp)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:mmvisionocrparagraphdet.DocLayoutResp)
    MergeFrom(*source);
  }
}

void DocLayoutResp::MergeFrom(const DocLayoutResp& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:mmvisionocrparagraphdet.DocLayoutResp)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 15u) {
    if (cached_has_bits & 0x00000001u) {
      set_has_word_bytes();
      word_bytes_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.word_bytes_);
    }
    if (cached_has_bits & 0x00000002u) {
      set_has_content();
      content_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.content_);
    }
    if (cached_has_bits & 0x00000004u) {
      set_has_b64_data();
      b64_data_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.b64_data_);
    }
    if (cached_has_bits & 0x00000008u) {
      set_has_title();
      title_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.title_);
    }
  }
}

void DocLayoutResp::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:mmvisionocrparagraphdet.DocLayoutResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DocLayoutResp::CopyFrom(const DocLayoutResp& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:mmvisionocrparagraphdet.DocLayoutResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DocLayoutResp::IsInitialized() const {
  return true;
}

void DocLayoutResp::Swap(DocLayoutResp* other) {
  if (other == this) return;
  InternalSwap(other);
}
void DocLayoutResp::InternalSwap(DocLayoutResp* other) {
  using std::swap;
  word_bytes_.Swap(&other->word_bytes_);
  content_.Swap(&other->content_);
  b64_data_.Swap(&other->b64_data_);
  title_.Swap(&other->title_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata DocLayoutResp::GetMetadata() const {
  protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

MMVisionOcrParagraphDet::~MMVisionOcrParagraphDet() {}

const ::google::protobuf::ServiceDescriptor* MMVisionOcrParagraphDet::descriptor() {
  protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::file_level_service_descriptors[0];
}

const ::google::protobuf::ServiceDescriptor* MMVisionOcrParagraphDet::GetDescriptor() {
  return descriptor();
}

void MMVisionOcrParagraphDet::Ping(::google::protobuf::RpcController* controller,
                         const ::mmvisionocrparagraphdet::PingReq*,
                         ::mmvisionocrparagraphdet::PingResp*,
                         ::google::protobuf::Closure* done) {
  controller->SetFailed("Method Ping() not implemented.");
  done->Run();
}

void MMVisionOcrParagraphDet::GetParaBoxes(::google::protobuf::RpcController* controller,
                         const ::mmvisionocrparagraphdet::ParagraphDetReq*,
                         ::mmvisionocrparagraphdet::ParagraphDetResp*,
                         ::google::protobuf::Closure* done) {
  controller->SetFailed("Method GetParaBoxes() not implemented.");
  done->Run();
}

void MMVisionOcrParagraphDet::GetDocLayout(::google::protobuf::RpcController* controller,
                         const ::mmvisionocrparagraphdet::DocLayoutReq*,
                         ::mmvisionocrparagraphdet::DocLayoutResp*,
                         ::google::protobuf::Closure* done) {
  controller->SetFailed("Method GetDocLayout() not implemented.");
  done->Run();
}

void MMVisionOcrParagraphDet::CallMethod(const ::google::protobuf::MethodDescriptor* method,
                             ::google::protobuf::RpcController* controller,
                             const ::google::protobuf::Message* request,
                             ::google::protobuf::Message* response,
                             ::google::protobuf::Closure* done) {
  GOOGLE_DCHECK_EQ(method->service(), protobuf_mmbiz_2fmmbizocr_2fsvr_2fmmvisionocrparagraphdet_2fmmvisionocrparagraphdet_2eproto::file_level_service_descriptors[0]);
  switch(method->index()) {
    case 0:
      Ping(controller,
             ::google::protobuf::down_cast<const ::mmvisionocrparagraphdet::PingReq*>(request),
             ::google::protobuf::down_cast< ::mmvisionocrparagraphdet::PingResp*>(response),
             done);
      break;
    case 1:
      GetParaBoxes(controller,
             ::google::protobuf::down_cast<const ::mmvisionocrparagraphdet::ParagraphDetReq*>(request),
             ::google::protobuf::down_cast< ::mmvisionocrparagraphdet::ParagraphDetResp*>(response),
             done);
      break;
    case 2:
      GetDocLayout(controller,
             ::google::protobuf::down_cast<const ::mmvisionocrparagraphdet::DocLayoutReq*>(request),
             ::google::protobuf::down_cast< ::mmvisionocrparagraphdet::DocLayoutResp*>(response),
             done);
      break;
    default:
      GOOGLE_LOG(FATAL) << "Bad method index; this should never happen.";
      break;
  }
}

const ::google::protobuf::Message& MMVisionOcrParagraphDet::GetRequestPrototype(
    const ::google::protobuf::MethodDescriptor* method) const {
  GOOGLE_DCHECK_EQ(method->service(), descriptor());
  switch(method->index()) {
    case 0:
      return ::mmvisionocrparagraphdet::PingReq::default_instance();
    case 1:
      return ::mmvisionocrparagraphdet::ParagraphDetReq::default_instance();
    case 2:
      return ::mmvisionocrparagraphdet::DocLayoutReq::default_instance();
    default:
      GOOGLE_LOG(FATAL) << "Bad method index; this should never happen.";
      return *::google::protobuf::MessageFactory::generated_factory()
          ->GetPrototype(method->input_type());
  }
}

const ::google::protobuf::Message& MMVisionOcrParagraphDet::GetResponsePrototype(
    const ::google::protobuf::MethodDescriptor* method) const {
  GOOGLE_DCHECK_EQ(method->service(), descriptor());
  switch(method->index()) {
    case 0:
      return ::mmvisionocrparagraphdet::PingResp::default_instance();
    case 1:
      return ::mmvisionocrparagraphdet::ParagraphDetResp::default_instance();
    case 2:
      return ::mmvisionocrparagraphdet::DocLayoutResp::default_instance();
    default:
      GOOGLE_LOG(FATAL) << "Bad method index; this should never happen.";
      return *::google::protobuf::MessageFactory::generated_factory()
          ->GetPrototype(method->output_type());
  }
}

MMVisionOcrParagraphDet_Stub::MMVisionOcrParagraphDet_Stub(::google::protobuf::RpcChannel* channel)
  : channel_(channel), owns_channel_(false) {}
MMVisionOcrParagraphDet_Stub::MMVisionOcrParagraphDet_Stub(
    ::google::protobuf::RpcChannel* channel,
    ::google::protobuf::Service::ChannelOwnership ownership)
  : channel_(channel),
    owns_channel_(ownership == ::google::protobuf::Service::STUB_OWNS_CHANNEL) {}
MMVisionOcrParagraphDet_Stub::~MMVisionOcrParagraphDet_Stub() {
  if (owns_channel_) delete channel_;
}

void MMVisionOcrParagraphDet_Stub::Ping(::google::protobuf::RpcController* controller,
                              const ::mmvisionocrparagraphdet::PingReq* request,
                              ::mmvisionocrparagraphdet::PingResp* response,
                              ::google::protobuf::Closure* done) {
  channel_->CallMethod(descriptor()->method(0),
                       controller, request, response, done);
}
void MMVisionOcrParagraphDet_Stub::GetParaBoxes(::google::protobuf::RpcController* controller,
                              const ::mmvisionocrparagraphdet::ParagraphDetReq* request,
                              ::mmvisionocrparagraphdet::ParagraphDetResp* response,
                              ::google::protobuf::Closure* done) {
  channel_->CallMethod(descriptor()->method(1),
                       controller, request, response, done);
}
void MMVisionOcrParagraphDet_Stub::GetDocLayout(::google::protobuf::RpcController* controller,
                              const ::mmvisionocrparagraphdet::DocLayoutReq* request,
                              ::mmvisionocrparagraphdet::DocLayoutResp* response,
                              ::google::protobuf::Closure* done) {
  channel_->CallMethod(descriptor()->method(2),
                       controller, request, response, done);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace mmvisionocrparagraphdet

// @@protoc_insertion_point(global_scope)
