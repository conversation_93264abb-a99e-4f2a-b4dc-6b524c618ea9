"""
Proxy requests to avoid SSRF
"""

import logging
import time

import httpx

import json
from configs import dify_config
from wx.wxmesh_request_bridge import *
from flask import g

from httpx._transports.default import (
    httpcore, typing, map_httpcore_exceptions, ResponseStream,
    HTTPTransport, SyncByteStream, Request, Response
)
from httpx import  ConnectTimeout,TimeoutException

SSRF_DEFAULT_MAX_RETRIES = dify_config.SSRF_DEFAULT_MAX_RETRIES

# proxy_mounts = (
#     {
#         "http://": httpx.HTTPTransport(proxy=dify_config.SSRF_PROXY_HTTP_URL),
#         "https://": httpx.HTTPTransport(proxy=dify_config.SSRF_PROXY_HTTPS_URL),
#     }
#     if dify_config.SSRF_PROXY_HTTP_URL and dify_config.SSRF_PROXY_HTTPS_URL
#     else None
# )

BACKOFF_FACTOR = 0.5
STATUS_FORCELIST = [429, 500, 502, 503, 504]


class CustomHostsTransport(HTTPTransport):
    def __init__(self, *args, domains={}, **kwargs) -> None:
        self.domains = {}
        for k, v in domains.items():
            self.domains[k.encode()] = v.encode()

        super().__init__(*args, **kwargs)

    def handle_request(self, request: Request) -> Response:
        assert isinstance(request.stream, SyncByteStream)

        host = request.url.raw_host
        req = httpcore.Request(
            method=request.method,
            url=httpcore.URL(
                scheme=request.url.raw_scheme,
                host=self.domains.get(host, host),
                port=request.url.port,
                target=request.url.raw_path,
            ),
            headers=request.headers.raw,
            content=request.stream,
            extensions=request.extensions,
        )
        with map_httpcore_exceptions():
            resp = self._pool.handle_request(req)

        assert isinstance(resp.stream, typing.Iterable)

        return Response(
            status_code=resp.status,
            headers=resp.headers,
            stream=ResponseStream(resp.stream),
            extensions=resp.extensions,
        )

transport = CustomHostsTransport(
    domains={"mmbizcos-1307796349.cos.ap-shanghai.myqcloud.com": "***************"}
)

class MaxRetriesExceededError(Exception):
    """Raised when the maximum number of retries is exceeded."""

    pass


def check_custom_domain_resolve(url):
    if url.startswith("http://mmbizcos-1307796349.cos.ap-shanghai.myqcloud.com"):
        return True
    return False


def make_request(method, url, max_retries=SSRF_DEFAULT_MAX_RETRIES, **kwargs):
    if "allow_redirects" in kwargs:
        allow_redirects = kwargs.pop("allow_redirects")
        if "follow_redirects" not in kwargs:
            kwargs["follow_redirects"] = allow_redirects

    if "timeout" not in kwargs:
        kwargs["timeout"] = httpx.Timeout(
            timeout=dify_config.SSRF_DEFAULT_TIME_OUT,
            connect=dify_config.SSRF_DEFAULT_CONNECT_TIME_OUT,
            read=dify_config.SSRF_DEFAULT_READ_TIME_OUT,
            write=dify_config.SSRF_DEFAULT_WRITE_TIME_OUT,
        )

    if url.startswith(
        "https://channels.weixin.qq.com/shop/commkf/downloadmedia"
    ) or url.startswith("https://store.weixin.qq.com/shop/commkf/downloadmedia"):
        if "headers" not in kwargs:
            kwargs["headers"] = {}
        kwargs["headers"].update({"proxy": "1"})

    retries = 0
    stream = kwargs.pop("stream", False)

    def get_conf_from_header(k:str)->bool:
        flag=False
        if 'headers' in kwargs:
            if k in kwargs['headers']:
                if kwargs['headers'][k].strip() in ['1','true'] :
                    logging.info(f"[kris] open flag '{k}'")
                    flag=True
                del kwargs['headers'][k] # 不要污染请求 header
        return flag

    conf_ConnectTimeout_no_retry:bool=get_conf_from_header("conf_ConnectTimeout_no_retry")
    conf_TimeoutException_no_retry:bool=get_conf_from_header("conf_TimeoutException_no_retry")


    start_=time.time()
    report_data={}

    try:
        req_json_str=repr(kwargs.get('json',''))
        try:
            req_json_str=json.dumps(kwargs.get('json',{}),ensure_ascii=False,default=repr)
        except Exception as e:
            pass 
        req_json_str_tail='' 
        try:
            # 2025.3.3 更好的看日志,wecube 会截断,所以多报个 tail
            if len(req_json_str)>2987:
                req_json_str_tail=req_json_str[-2987:] 
        except Exception as e:
            pass

        report_data={
                'biz_id':14780,
                "time":int(time.time()),
                "path":"",
                'module_name':"",
                'report_ip': os.getenv("POD_IP", ''),
                'report_module': os.getenv("MODULE", '-'),
                'report_machine': os.getenv("HOSTNAME", ''),
                'method':method,
                'url':url,
                'req_headers':json.dumps(kwargs.get("headers",{})),
                'req_data':repr(kwargs.get('data','')),
                'req_json':req_json_str,
                'req_json_tail':req_json_str_tail,
                'req_args':repr(kwargs.get('params','')),
                'timeout':repr(kwargs.get('timeout','')),
                "api_request_id":str(threading.main_thread().ident),
            }
    except Exception as e:
        logging.error(e)

    try:
        if hasattr(g, 'extend_info'):
            with g.extend_info['lock']:
                report_data['rtx'] = str(g.extend_info.get("rtx", ''))
                report_data['app_id'] = str(g.extend_info.get("app_id", ''))
                report_data['uin'] = int(g.extend_info.get("uin", '0'))
                report_data['is_test'] = g.extend_info.get("is_test", '')
                report_data['debug_info'] = g.extend_info.get("debug_info", '')
                report_data['conversation_id'] = str(g.extend_info.get("conversation_id", ''))
                report_data['traceid'] = str(g.extend_info.get("traceid", ''))
                report_data['message_id'] = str(g.extend_info.get("message_id", ''))
                report_data['workflow_id'] = str(g.extend_info.get("workflow_id", ''))
                report_data['workflow_run_id'] = str(g.extend_info.get("workflow_run_id", ''))
                report_data['iterator'] = g.extend_info.get("current", {}).get("iterator", 0)
                report_data['business_message_id'] = g.extend_info.get("business_message_id", '')
                report_data['business_session_id'] = g.extend_info.get("business_session_id", '')
                report_data['llm_node_name'] = g.extend_info.get("llm_node_name", '')
    except Exception as e:
        logging.error(f"[nickwu] get extend_info fail, {repr(e)}")
 
    response=None
    retry_history=[]
    while retries <= max_retries:
        start_=time.time()
        try:
            # todo(kris): remove ssrf
            # if dify_config.SSRF_PROXY_ALL_URL:
            #     with httpx.Client(proxy=dify_config.SSRF_PROXY_ALL_URL) as client:
            #         response = client.request(method=method, url=url, **kwargs)
            # elif proxy_mounts:
            #     with httpx.Client(mounts=proxy_mounts) as client:
            #         response = client.request(method=method, url=url, **kwargs)
            # else:

            # todo(kris): 2025.1.9 前端设置 proxy 就行..
            proxy=None
            original_proxy_header_pair = None
            try:
                proxy_header=[k for k,v in kwargs.get("headers",{}).items() if k.lower().strip()=="proxy" and v.strip()=="1"]
                if len(proxy_header)>0:
                    proxy='http://shanghai-mmhttpproxy.woa.com:11113' 
                    report_data['proxy']='1'
                    logging.info("[kris] use proxy")
                    original_proxy_header_pair = (proxy_header[0], kwargs['headers'][proxy_header[0]])
                    del kwargs['headers'][proxy_header[0]]
            except Exception as e:
                logging.error(e)
            extend_httpx_kwargs = {}
            # 需要手动进行dns解析
            if check_custom_domain_resolve(url):
                extend_httpx_kwargs["transport"] = transport
            with httpx.Client(proxy=proxy, **extend_httpx_kwargs) as client:
                response = client.request(method=method, url=url, **kwargs)

            if response.status_code not in STATUS_FORCELIST:
                if report_data is not None:
                    try:
                        report_data['time_cost_ms']=int((time.time()-start_)*1000)
                        report_data['response']=response.text
                        report_data['http_status_code']=response.status_code
                        if response.status_code==405 and method=='HEAD': # HEAD 405 不报错 因为小店有些图片不支持head方法
                            return response
                        async_report(report_data)
                    except Exception as e:
                        logging.error(e)
                return response
            else:
                logging.warning(f"Received status code {response.status_code} for URL {url} which is in the force list")
                retry_history.append(f"force_retry_http_status_code[{response.status_code}]({int(time.time() - start_)}s)")
        except ConnectTimeout as e:
            if conf_ConnectTimeout_no_retry:
                logging.error(f"conf_ConnectTimeout_no_retry err'{repr(e)}'")
                break
            logging.warning(f"[ConnectTimeout] Request to URL {url} failed on attempt {retries + 1}: {e}")
            retry_history.append(f"ConnectTimeout({int(time.time()-start_)}s)")
        except TimeoutException as e:
            if conf_TimeoutException_no_retry:
                logging.error(f"conf_TimeoutException_no_retry err'{repr(e)}'")
                break
            logging.warning(f"[TimeoutException] Request to URL {url} failed on attempt {retries + 1}: {e}")
            retry_history.append(f"TimeoutException(except for ConnectTimeout)({int(time.time()-start_)}s)")
        except httpx.RequestError as e:
            logging.warning(f"Request to URL {url} failed on attempt {retries + 1}: {e}")
            retry_history.append(f"RequestError[{repr(e)}]({int(time.time()-start_)}s)")
        finally:
            if original_proxy_header_pair:
                kwargs["headers"][original_proxy_header_pair[0]] = original_proxy_header_pair[1]

        retries += 1
        if retries <= max_retries:
            time.sleep(BACKOFF_FACTOR * (2 ** (retries - 1)))

    retry_history_str=json.dumps(retry_history,ensure_ascii=False,indent=4)
    try:
        report_data['time_cost_ms']=int((time.time()-start_)*1000)
        if response is not None:
            report_data['response']=response.text
            report_data['http_status_code']=response.status_code
            report_data['retry_history']=retry_history_str
        async_report(report_data)
    except Exception as e:
        logging.error(e)
    raise MaxRetriesExceededError(f"Reached maximum retries ({retries}) for URL {url}\n{retry_history_str}")


def get(url, max_retries=SSRF_DEFAULT_MAX_RETRIES, **kwargs):
    return make_request("GET", url, max_retries=max_retries, **kwargs)


def post(url, max_retries=SSRF_DEFAULT_MAX_RETRIES, **kwargs):
    return make_request("POST", url, max_retries=max_retries, **kwargs)


def put(url, max_retries=SSRF_DEFAULT_MAX_RETRIES, **kwargs):
    return make_request("PUT", url, max_retries=max_retries, **kwargs)


def patch(url, max_retries=SSRF_DEFAULT_MAX_RETRIES, **kwargs):
    return make_request("PATCH", url, max_retries=max_retries, **kwargs)


def delete(url, max_retries=SSRF_DEFAULT_MAX_RETRIES, **kwargs):
    return make_request("DELETE", url, max_retries=max_retries, **kwargs)


def head(url, max_retries=SSRF_DEFAULT_MAX_RETRIES, **kwargs):
    return make_request("HEAD", url, max_retries=max_retries, **kwargs)
