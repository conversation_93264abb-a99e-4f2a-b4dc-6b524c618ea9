import logging
import os
from urllib.parse import urlencode
from collections.abc import Mapping
from enum import StrEnum
from threading import Lock
from typing import Any, Optional
from contextlib import redirect_stdout
import io

from httpx import Timeout, post
from pydantic import BaseModel
from yarl import URL

from configs import dify_config
from core.helper.code_executor.javascript.javascript_transformer import NodeJsTemplateTransformer
from core.helper.code_executor.jinja2.jinja2_transformer import Jinja2TemplateTransformer
from core.helper.code_executor.python3.python3_transformer import Python3TemplateTransformer
from core.helper.code_executor.template_transformer import TemplateTransformer

import time
from wx.rainbow_conf import hit_rate_limit, get_svrkit_sandbox_switch
from flask import g
from wx.mesh.svrkit_client import (
    check_implemented,call_svrkit
)
from wx.shared_context import cur_node_ctx
from concurrent.futures import ThreadPoolExecutor
global_async_executor=ThreadPoolExecutor(max_workers=4)

logger = logging.getLogger(__name__)

import subprocess

def capture_via_subprocess(code):
    result = subprocess.run(
        ["python", "-c", code],
        stdout=subprocess.PIPE,
        text=True
    )
    return result.stdout


def exec_with_capture(code):
    f = io.StringIO()
    with redirect_stdout(f):
        globals_dict = {**globals(), **(None or {'g': g})}
        exec(code, globals_dict, globals_dict)
    return f.getvalue().strip()

class CodeExecutionError(Exception):
    pass


class CodeExecutionResponse(BaseModel):
    class Data(BaseModel):
        stdout: Optional[str] = None
        error: Optional[str] = None

    code: int
    message: str
    data: Data


class CodeLanguage(StrEnum):
    PYTHON3 = "python3"
    JINJA2 = "jinja2"
    JAVASCRIPT = "javascript"


def execute_code_svrkit(language: CodeLanguage, preload: str, code: str, uin: int, meshuin: int, meshid: int, code_req_id: str) -> str:
    """
    Execute code
    :param language: code language
    :param preload: preload code
    :param code: code
    :return:
    """

    try:
        data = {
            "id": code_req_id,
            "code": code,
            "preload": preload,
            "enable_network": True,
        }

        x_wxmesh_result, json_str_res = call_svrkit(
            meshuin or uin, meshid, "mmassistantpysandboxsvr", "RunCodeNoResp", data
        )
        if x_wxmesh_result != 0:
            logging.error(
                f"[hanszhao] RunCodeNoResp fail, x_wxmesh_result:{x_wxmesh_result}"
            )
            return None
        response = {}
        try:
            response = json_str_res.json()
        except:
            logging.error(f"[hanszhao] Failed to parse response: {str(response)}")
            return ""

        if (code := response.get("code")) != 0:
            logging.error(f"[hanszhao] Got error id:{response.get('id', '')} code: {code}. Got error msg: {response.get('message', '')}")
            logging.error(f"[hanszhao] fail response: {str(response)}")
            return ""

        # response = CodeExecutionResponse(**response)

        # if response.data.error:
        #     logging.error(f"[hanszhao] execute code fail: {response.data.error}")
    
    except Exception as e:
        logging.error(f"[hanszhao] RunCodeNoResp fail, {repr(e)}")
        return None

def async_execute_code_svrkit(language: CodeLanguage, preload: str, code: str, uin: int, meshuin: int, meshid: int, **kwargs):
    try:
        if not language == CodeLanguage.PYTHON3:
            return
        app_id = kwargs.get("app_id", "")
        if not get_svrkit_sandbox_switch(app_id):
            return
        if hit_rate_limit("svrkit_sandbox"):
            return
        code_req_id = kwargs.get("code_req_id", "")
        before_req_time = int(time.time() * 1000)
        response = execute_code_svrkit(language, preload, code, uin, meshuin, meshid, code_req_id)
        cost = int(time.time() * 1000) - before_req_time
        logging.info(f"[hanszhao] execute_code_svrkit cost:{cost} appid:{app_id}")
    except CodeExecutionError as e:
        logging.error(f"[hanszhao] execute_code_svrkit fail, {repr(e)}")

class CodeExecutor:
    dependencies_cache = {}
    dependencies_cache_lock = Lock()

    code_template_transformers: dict[CodeLanguage, type[TemplateTransformer]] = {
        CodeLanguage.PYTHON3: Python3TemplateTransformer,
        CodeLanguage.JINJA2: Jinja2TemplateTransformer,
        CodeLanguage.JAVASCRIPT: NodeJsTemplateTransformer,
    }

    code_language_to_running_language = {
        CodeLanguage.JAVASCRIPT: "nodejs",
        CodeLanguage.JINJA2: CodeLanguage.PYTHON3,
        CodeLanguage.PYTHON3: CodeLanguage.PYTHON3,
    }

    supported_dependencies_languages: set[CodeLanguage] = {CodeLanguage.PYTHON3}
    

    @classmethod
    def execute_code(cls, language: CodeLanguage, preload: str, code: str) -> str:
        """
        Execute code
        :param language: code language
        :param code: code
        :return:
        """
        host=str(dify_config.CODE_EXECUTION_ENDPOINT)
        url = URL(host) / "v1" / "sandbox" / "run"

        headers = {"X-Api-Key": dify_config.CODE_EXECUTION_API_KEY}

        import re
        # todo(kris): # use-proxy
        matcher0 = re.findall(r'^#\s+use-proxy\s*$', code, re.MULTILINE)
        if matcher0:
            code = """import os
proxy = 'http://shanghai-mmhttpproxy.woa.com:11113'
os.environ["http_proxy"] = proxy
os.environ["https_proxy"] = proxy
os.environ["NO_PROXY"] = os.environ["no_proxy"] = "localhost, 127.0.0.1/8, ::1, mirrors.tencent.com"

""" + code

        data = {
            "language": cls.code_language_to_running_language.get(language),
            "code": code,
            "preload": preload,
            "enable_network": True,
        }

        try:
            if os.environ.get('RUNTIME') == 'DEVCLOUD':
                if os.environ.get('SANDBOX_CODE_EXECUTION', '') != '':
                    host=str(os.environ.get('SANDBOX_CODE_EXECUTION', ''))
                    url = URL(host) / "v1" / "sandbox" / "run"
                else:
                    params=urlencode({"url":str(url)})
                    url=f"http://wxassistant.weixin.woa.com/service/dev/proxy?{params}"

            response = post(
                str(url),
                json=data,
                headers=headers,
                timeout=Timeout(
                    connect=dify_config.CODE_EXECUTION_CONNECT_TIMEOUT,
                    read=dify_config.CODE_EXECUTION_READ_TIMEOUT,
                    write=dify_config.CODE_EXECUTION_WRITE_TIMEOUT,
                    pool=None,
                ),
            )
            logging.info(f"[sandbox] data:{data} resp:{response.text}")
            if response.status_code == 503:
                raise CodeExecutionError("Code execution service is unavailable")
            elif response.status_code != 200:
                raise Exception(
                    f"Failed to execute code, got status code {response.status_code},"
                    f" please check if the sandbox service is running"
                )
        except CodeExecutionError as e:
            raise e
        except Exception as e:
            raise CodeExecutionError(
                "Failed to execute code, which is likely a network issue,"
                " please check if the sandbox service is running."
                f" ( Error: {str(e)} )"
            )

        try:
            response = response.json()
        except:
            raise CodeExecutionError("Failed to parse response")

        if (code := response.get("code")) != 0:
            raise CodeExecutionError(f"Got error code: {code}. Got error msg: {response.get('message')}")

        response = CodeExecutionResponse(**response)

        if response.data.error:
            raise CodeExecutionError(response.data.error)

        return response.data.stdout or ""

    @classmethod
    def execute_workflow_code_template(cls, language: CodeLanguage, code: str, inputs: Mapping[str, Any]) -> dict:
        result, debug = cls.execute_workflow_code_template_with_debug(language, code, inputs)
        return result

    @classmethod
    def execute_workflow_code_template_with_debug(cls, language: CodeLanguage, code: str, inputs: Mapping[str, Any]) -> tuple[dict, dict]:
        """
        Execute code
        :param language: code language
        :param code: code
        :param inputs: inputs
        :return: result, debug
        """
        template_transformer = cls.code_template_transformers.get(language)
        if not template_transformer:
            raise CodeExecutionError(f"Unsupported language {language}")

        runner, preload = template_transformer.transform_caller(code, inputs)

        try:
            before_req_time = int(time.time() * 1000)
            node_name=cur_node_ctx.get().name
            if node_name.startswith("local_subprocess/"):
                try:
                    response = capture_via_subprocess(runner)
                    logging.info(
                        f"[kris][mode:local_subprocess] local_code_eval result '{response}'")
                except Exception as e:
                    logging.error(
                        f"[kris][mode:local_subprocess] local_code_eval fail {e}")
                    response = cls.execute_code(language, preload, runner)
            elif node_name.startswith("local_exec/"):
                logging.info(f"[kris][mode:local_exec] start local_code_eval , '{runner} g:'{g.extend_info}'")    
                response=exec_with_capture(runner)
                logging.info(f"[kris][mode:local_exec] local_code_eval result '{response}'")    
            else:
                # default mode, use remote sandbox service
                response = cls.execute_code(language, preload, runner)
            cost = int(time.time() * 1000) - before_req_time
        except CodeExecutionError as e:
            raise e

        debug = {
            "stdout": template_transformer.extract_stdout_from_response(response),
            "execute_cost": cost,
        }

        try:
            if language == CodeLanguage.PYTHON3:
                before_req_time = int(time.time() * 1000)
                logging.info(f"[hanszhao] async_execute_code_svrkit:")
                # 异步调用需要赋值成员变量，外部调用未触发 __init__
                app_id = str(g.extend_info.get("app_id", ''))
                traceid = str(g.extend_info.get("traceid", ''))
                code_req_id = "##".join([app_id, traceid])
                uin = int(g.extend_info.get("uin", '0'))
                meshuin = int(g.extend_info.get("meshuin", '0'))
                meshid = int(g.extend_info.get("meshid", '0'))
                global global_async_executor
                global_async_executor.submit(async_execute_code_svrkit,language, preload, runner, uin, meshuin, meshid, code_req_id=code_req_id, app_id=app_id)
                cost = int(time.time() * 1000) - before_req_time
                debug["svrkit_async_cost"] = cost
        except Exception as e:
            logging.error(f"[hanszhao] async_execute_code_svrkit failed: {repr(e)}")

        return template_transformer.transform_response(response), debug
