ENGLISH_REACT_COMPLETION_PROMPT_TEMPLATES = """尽可能地热心并准确地回复人类用户 

{{instruction}}

你可以使用以下工具：

{{tools}}

Use a json blob to specify a tool by providing an action key (tool name) and an action_input key (tool input).
Valid "action" values: "Final Answer" or {{tool_names}}

Provide only ONE action per $JSON_BLOB, as shown:

```
{
  "action": $TOOL_NAME,
  "action_input": $ACTION_INPUT
}
```

按照如下格式：

Question: 需要回答的问题
Thought: consider previous and subsequent steps
Action:
```
$JSON_BLOB
```
Observation: action的结果
... (repeat Thought/Action/Observation N times)
Thought: 我知道如何回应了
Action:
```
{
  "action": "Final Answer",
  "action_input": "最终给人类用户的回应"
}
```

开始！注意！始终以单个action的json blob回应，注意检查json blob的参数。必要时使用工具，适当时直接回应。Format is Action:```$JSON_BLOB```then Observation:.
{{historic_messages}}
Question: {{query}}
{{agent_scratchpad}}
Thought:"""  # noqa: E501


ENGLISH_REACT_COMPLETION_AGENT_SCRATCHPAD_TEMPLATES = """Observation: {{observation}}
Thought:"""

ENGLISH_REACT_CHAT_PROMPT_TEMPLATES = """尽可能地热心并准确地回复人类用户 

{{instruction}}

你可以使用以下工具：

{{tools}}

Use a json blob to specify a tool by providing an action key (tool name) and an action_input key (tool input).
Valid "action" values: "Final Answer" or {{tool_names}}

Provide only ONE action per $JSON_BLOB, as shown:

```
{
  "action": $TOOL_NAME,
  "action_input": $ACTION_INPUT
}
```

按照如下格式：

Question: 需要回答的问题
Thought: consider previous and subsequent steps
Action:
```
$JSON_BLOB
```
Observation: action的结果
... (repeat Thought/Action/Observation N times, Maximum of three repetitions.)
Thought: 我知道如何回应了
Action:
```
{
  "action": "Final Answer",
  "action_input": "最终给人类用户的回应"
}
```

开始！注意！始终以单个action的json blob回应，注意检查json blob的参数。必要时使用工具，适当时直接回应。Format is Action:```$JSON_BLOB```then Observation:.
"""  # noqa: E501


ENGLISH_REACT_CHAT_AGENT_SCRATCHPAD_TEMPLATES = ""

REACT_PROMPT_TEMPLATES = {
    "english": {
        "chat": {
            "prompt": ENGLISH_REACT_CHAT_PROMPT_TEMPLATES,
            "agent_scratchpad": ENGLISH_REACT_CHAT_AGENT_SCRATCHPAD_TEMPLATES,
        },
        "completion": {
            "prompt": ENGLISH_REACT_COMPLETION_PROMPT_TEMPLATES,
            "agent_scratchpad": ENGLISH_REACT_COMPLETION_AGENT_SCRATCHPAD_TEMPLATES,
        },
    }
}


